package com.wonderslate.data

import grails.transaction.Transactional
import groovy.sql.Sql
import com.wonderslate.data.ObjectiveMst

@Transactional
class WpmainService {
    def grailsApplication

    def serviceMethod() {

    }

    def getExerciseSolutions(Long chapterId) {
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql = new Sql(dataSource)

        def query = '''
            SELECT om.id, om.question, om.answer, om.answerDescription, om.gptExplanation, om.qType
            FROM resource_dtl rd
            JOIN objective_mst om ON rd.res_link = om.quiz_id
            WHERE rd.chapter_id = ?
            AND rd.resource_name = 'Exercise Solutions'
            AND rd.res_type = 'QA'
            ORDER BY om.id
        '''

        def results = sql.rows(query, [chapterId])
        sql.close()
        return results
    }

    def getQuestionBankData(Long chapterId) {
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql = new Sql(dataSource)

        // Get QnA questions
        def qnaQuery = '''
            SELECT om.id, om.question, om.answer, om.answerDescription, om.gptExplanation, om.qType,
                   om.option1, om.option2, om.option3, om.option4, om.option5
            FROM resource_dtl rd
            JOIN objective_mst om ON rd.res_link = om.quiz_id
            WHERE rd.chapter_id = ?
            AND rd.resource_name = 'QuestionBank QnA'
            AND rd.res_type = 'QA'
            ORDER BY om.qType, om.id
        '''

        // Get MCQ questions
        def mcqQuery = '''
            SELECT om.id, om.question, om.answer, om.answerDescription, om.gptExplanation, om.qType,
                   om.option1, om.option2, om.option3, om.option4, om.option5
            FROM resource_dtl rd
            JOIN objective_mst om ON rd.res_link = om.quiz_id
            WHERE rd.chapter_id = ?
            AND rd.resource_name = 'QuestionBank MCQs'
            AND rd.res_type = 'Multiple Choice Questions'
            ORDER BY om.id
        '''

        def qnaResults = sql.rows(qnaQuery, [chapterId])
        def mcqResults = sql.rows(mcqQuery, [chapterId])

        sql.close()

        // Group QnA results by qType
        def groupedQnA = qnaResults.groupBy { it.qType }

        return [
            qnaQuestions: groupedQnA,
            mcqQuestions: mcqResults
        ]
    }

    def getQuestionTypeCounts(Long chapterId) {
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql = new Sql(dataSource)

        def query = '''
            SELECT om.qType, COUNT(*) as count
            FROM resource_dtl rd
            JOIN objective_mst om ON rd.res_link = om.quiz_id
            WHERE rd.chapter_id = ?
            AND rd.resource_name IN ('QuestionBank QnA', 'QuestionBank MCQs')
            AND rd.res_type IN ('QA', 'Multiple Choice Questions')
            GROUP BY om.qType
        '''

        def results = sql.rows(query, [chapterId])
        sql.close()

        def counts = [:]
        results.each { row ->
            counts[row.qType] = row.count
        }

        return counts
    }

    def getExplanation(Long questionId) {
        ObjectiveMst question = ObjectiveMst.findById(questionId)
        return question?.gptExplanation ?: question?.answerDescription
    }
}
