package com.wonderslate.data

import grails.transaction.Transactional
import com.wonderslate.data.ObjectiveMst
import com.wonderslate.sqlutil.SafeSql

@Transactional
class WpmainService {
    def grailsApplication

    def serviceMethod() {

    }

    def getExerciseSolutions(Long chapterId) {
        String sql = "SELECT om.id, om.question, om.answer, om.answer_description answerDescription, om.gpt_explanation gptExplanation, om.q_type qType " +
                "FROM resource_dtl rd " +
                "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                "WHERE rd.chapter_id = " + chapterId + " " +
                "AND rd.resource_name = 'Exercise Solutions' " +
                "AND rd.res_type = 'QA' " +
                "ORDER BY om.id"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        return results
    }

    def getQuestionBankData(Long chapterId) {
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)

        // Get QnA questions
        String qnaQuery = "SELECT om.id, om.question, om.answer, om.answer_description answerDescription, om.gpt_explanation gptExplanation, om.q_type qType, " +
                "om.option1, om.option2, om.option3, om.option4, om.option5 " +
                "FROM resource_dtl rd " +
                "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                "WHERE rd.chapter_id = " + chapterId + " " +
                "AND rd.resource_name = 'QuestionBank QnA' " +
                "AND rd.res_type = 'QA' " +
                "ORDER BY om.q_type, om.id"

        // Get MCQ questions
        String mcqQuery = "SELECT om.id, om.question, om.answer, om.answer_description answerDescription, om.gpt_explanation gptExplanation, om.q_type qType, " +
                "om.option1, om.option2, om.option3, om.option4, om.option5 " +
                "FROM resource_dtl rd " +
                "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                "WHERE rd.chapter_id = " + chapterId + " " +
                "AND rd.resource_name = 'QuestionBank MCQs' " +
                "AND rd.res_type = 'Multiple Choice Questions' " +
                "ORDER BY om.id"

        def qnaResults = sql1.rows(qnaQuery)
        def mcqResults = sql1.rows(mcqQuery)

        // Group QnA results by qType
        def groupedQnA = qnaResults.groupBy { it.qType }

        return [
            qnaQuestions: groupedQnA,
            mcqQuestions: mcqResults
        ]
    }

    def getQuestionTypeCounts(Long chapterId) {
        String sql = "SELECT om.q_type qType, COUNT(*) as count " +
                "FROM resource_dtl rd " +
                "JOIN objective_mst om ON rd.res_link = om.quiz_id " +
                "WHERE rd.chapter_id = " + chapterId + " " +
                "AND rd.resource_name IN ('QuestionBank QnA', 'QuestionBank MCQs') " +
                "AND rd.res_type IN ('QA', 'Multiple Choice Questions') " +
                "GROUP BY om.q_type"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        def counts = [:]
        results.each { row ->
            counts[row.qType] = row.count
        }

        return counts
    }

    def getExplanation(Long questionId) {
        ObjectiveMst question = ObjectiveMst.findById(questionId)
        return question?.gptExplanation ?: question?.answerDescription
    }
}
