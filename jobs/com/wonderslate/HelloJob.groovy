package com.wonderslate

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.AutogptService
import com.wonderslate.log.TestTable
import grails.util.Environment
import org.quartz.JobExecutionContext
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class HelloJob  {
    // one line to stop accidental double-fires
    static concurrent = false      // (static in Quartz 2 plugin)
    AutogptService autogptService

    /** Define when the job should run */
    static triggers = {
        // Fires every 10 minutes, 10 seconds after app start
        simple name: 'gptTrigger',
                startDelay: 10000L,
                repeatInterval: 600000L
        // Fires every 30 minutes, 90 seconds after app start
        simple name: 'gptCheckTrigger',
                startDelay: 90000L,
                repeatInterval: 1800000L
    }

    private static final Logger log = LoggerFactory.getLogger(HelloJob)

    /** What the job actually does */
    def execute(JobExecutionContext context1) {
        def triggerName = context.trigger.key.name
        switch (triggerName) {
            case 'gptTrigger':
                println("Hello<PERSON><PERSON> (gptTrigger) says hello at ${new Date()}")
                try {
                   // autogptService.autoGPTRunner()
                } catch (Exception e) {
                    println("Error in helloTrigger: ${e.message}")
                }
                break

            case 'gptCheckTrigger':
                log.info "HelloJob (gptCheckTrigger) checking logs at ${new Date()}"
                try {
                    //  autogptService.checkPendingJobs()

                } catch (Exception e) {
                    println("Error in checkLogTrigger: ${e.message}")
                }
        // your real work goes here
    }
    }
}
