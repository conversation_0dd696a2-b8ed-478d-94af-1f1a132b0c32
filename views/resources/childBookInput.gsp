<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">Create child book of - ${booksMst.title}</h3>
                    <div class="form-group table-responsive" id="intrst-area">
                        <div class="row">
                            <div class="col-md-8 col-md-offset-2">
                                <div class="panel panel-default">
                                    <div class="panel-body">
                                        <div class="form-group">
                                            <label class="radio">
                                                <select name="publisher" id="publisher" class="form-control">
                                                    <option value="">Select Publisher</option>
                                                    <g:each in="${publishers}" var="publisher">
                                                        <option value="${publisher.id}">${publisher.name}</option>
                                                    </g:each>
                                                </select>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-8 col-md-offset-2">
                                <div class="panel panel-default">
                                    <div class="panel-body">
                                        <div class="form-group">
                                            <button type="button" class="btn btn-primary" onclick="createChildBook()">Create</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>
<script>
    function createChildBook() {
        var publisher = document.getElementById("publisher").value;
        if (!publisher) {
            alert("Please select a publisher.");
            return;
        }

        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="resources" action="createChildBook"  onSuccess='childBookCreated(data);'
        params="'bookId=${booksMst.id}&publisherId='+publisher"/>
    }

    function childBookCreated(data){
        alert(data.status);
        window.location.href="/book-create-new?bookId="+data.bookId;
    }

</script>
