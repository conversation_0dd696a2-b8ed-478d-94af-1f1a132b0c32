<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}

/* AI Book Migration specific styles */
.radio {
    margin-bottom: 10px;
}

.radio input[type="radio"] {
    margin-right: 8px;
}

.radio p {
    margin-left: 20px;
    margin-bottom: 5px;
}

#targetBookIdDiv {
    margin-top: 10px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

#statusMessage {
    margin-top: 20px;
}

.panel {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">AI Book Creation</h3>
                    <h4 class="text-center text-muted">${booksMst.title}</h4>
                    <div class="form-group table-responsive" id="intrst-area">
                        <div class="row">
                            <div class="col-md-8 col-md-offset-2">
                                <div class="panel panel-default">
                                    <div class="panel-body">
                                        <div class="form-group">
                                            <label class="radio">
                                                <input type="radio" name="migrationOption" value="createNew" checked>
                                                <strong>Create new AI book</strong>
                                            </label>
                                            <p class="text-muted">Creates a copy of the current book and converts it to AI format</p>
                                        </div>

                                        <div class="form-group">
                                            <label class="radio">
                                                <input type="radio" name="migrationOption" value="copyToExisting">
                                                <strong>Copy to Existing AI book</strong>
                                            </label>
                                            <p class="text-muted">Copy content to an existing book and convert it to AI format</p>
                                            <div id="targetBookIdDiv" style="display: none; margin-left: 20px;">
                                                <label for="targetBookId">Target Book ID:</label>
                                                <input type="number" id="targetBookId" class="form-control" style="width: 200px; display: inline-block;" placeholder="Enter book ID">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label class="radio">
                                                <input type="radio" name="migrationOption" value="convertCurrent">
                                                <strong>Convert to AI book</strong>
                                            </label>
                                            <p class="text-muted">Convert the current book directly to AI format</p>
                                        </div>

                                        <div class="form-group text-center">
                                            <button type="button" id="startMigration" class="btn btn-primary btn-lg">
                                                Start AI Book Creation
                                            </button>
                                        </div>

                                        <div id="statusMessage" class="alert" style="display: none;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<script>
$(document).ready(function() {
    var currentBookId = ${booksMst.id};

    // Handle radio button changes
    $('input[name="migrationOption"]').change(function() {
        if ($(this).val() === 'copyToExisting') {
            $('#targetBookIdDiv').show();
        } else {
            $('#targetBookIdDiv').hide();
            $('#targetBookId').val('');
        }
    });

    // Handle start migration button click
    $('#startMigration').click(function() {
        var selectedOption = $('input[name="migrationOption"]:checked').val();
        var targetBookId = $('#targetBookId').val();

        // Validation
        if (selectedOption === 'copyToExisting' && (!targetBookId || targetBookId.trim() === '')) {
            showMessage('Please enter a target book ID.', 'danger');
            return;
        }

        // Disable button and show loading
        $(this).prop('disabled', true).text('Processing...');
        showMessage('Starting AI book creation process...', 'info');

        // Execute the appropriate workflow
        if (selectedOption === 'createNew') {
            createNewAIBook();
        } else if (selectedOption === 'copyToExisting') {
            copyToExistingAIBook(targetBookId);
        } else if (selectedOption === 'convertCurrent') {
            convertCurrentToAI();
        }
    });

    function createNewAIBook() {
        // Step 1: Copy book
        $.ajax({
            url: '/resourceCreator/copyBook',
            type: 'POST',
            data: { bookId: currentBookId },
            success: function(response) {
                if (response.status === 'Success' && response.bookId) {
                    showMessage('Book copied successfully. Now migrating to AI format...', 'info');
                    // Step 2: Migrate to AI
                    migrateBookToAI(response.bookId);
                } else {
                    showMessage('Error copying book: ' + (response.status || 'Unknown error'), 'danger');
                    resetButton();
                }
            },
            error: function() {
                showMessage('Error occurred while copying book.', 'danger');
                resetButton();
            }
        });
    }

    function copyToExistingAIBook(targetBookId) {
        // Step 1: Copy to existing book
        $.ajax({
            url: '/resourceCreator/copyToExistingBook',
            type: 'POST',
            data: {
                sourceBookId: currentBookId,
                destBookId: targetBookId
            },
            success: function(response) {
                if (response.status === 'Success' && response.bookId) {
                    showMessage('Content copied to existing book successfully. Now migrating to AI format...', 'info');
                    // Step 2: Migrate to AI
                    migrateBookToAI(response.bookId);
                } else {
                    showMessage('Error copying to existing book: ' + (response.status || 'Unknown error'), 'danger');
                    resetButton();
                }
            },
            error: function() {
                showMessage('Error occurred while copying to existing book.', 'danger');
                resetButton();
            }
        });
    }

    function convertCurrentToAI() {
        // Direct migration of current book
        migrateBookToAI(currentBookId);
    }

    function migrateBookToAI(bookId) {
        $.ajax({
            url: '/promptLanguages/migrateBook',
            type: 'POST',
            data: { bookId: bookId },
            success: function(response) {
                var bookLink = '/book-create-new?bookId=' + bookId + '&printBooks=false';
                var successMessage = 'AI book creation completed successfully! ' +
                    '<a href="' + bookLink + '" class="btn btn-success btn-sm" target="_blank">Open AI Book</a>';
                showMessage(successMessage, 'success');
                resetButton();
            },
            error: function() {
                showMessage('Error occurred during AI migration.', 'danger');
                resetButton();
            }
        });
    }

    function showMessage(message, type) {
        var alertClass = 'alert-' + type;
        $('#statusMessage')
            .removeClass('alert-success alert-danger alert-info alert-warning')
            .addClass(alertClass)
            .html(message)
            .show();
    }

    function resetButton() {
        $('#startMigration').prop('disabled', false).text('Start AI Book Creation');
    }
});
</script>

</body>
</html>
