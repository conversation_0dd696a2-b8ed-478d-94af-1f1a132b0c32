<%@ page import="java.text.SimpleDateFormat" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${booksMst.title} - AI Ebook</title>
    <g:render template="/wonderpublish/loginChecker"></g:render>
    <g:render template="/${session['entryController']}/navheader_new"></g:render>

    <!-- KaTeX for math rendering -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        /* Global Styles */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            line-height: 1.6;
        }

        .ebook-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            min-height: calc(100vh - 160px);
        }

        /* Header Styles */
        .ebook-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .book-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .book-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-top: 10px;
        }

        /* Layout Styles */
        .ebook-layout {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Chapter Selection */
        .chapter-selection {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .chapter-selection h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .chapter-dropdown {
            width: 100%;
            max-width: 400px;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .chapter-dropdown:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .chapter-dropdown:hover {
            border-color: #667eea;
        }

        /* Content Area */
        .content-area {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        /* Loading Spinner */
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Chapter Summary */
        .chapter-summary {
            background: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .summary-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 25px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .question-type-nav {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 20px;
        }

        .type-nav-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            text-align: left;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .type-nav-item:hover {
            background: #f0f0f0;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #e9ecef;
        }

        .type-nav-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            color: white;
            flex-shrink: 0;
        }

        .type-nav-content {
            flex: 1;
        }

        .type-nav-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
            font-size: 1rem;
        }

        .type-nav-count {
            font-size: 0.9rem;
            color: #666;
        }

        /* Icon Colors */
        .icon-exercise { background: #4CAF50; }
        .icon-long-answer { background: #2196F3; }
        .icon-short-answer { background: #FF9800; }
        .icon-very-short { background: #9C27B0; }
        .icon-assertion { background: #607D8B; }
        .icon-problem { background: #795548; }
        .icon-mcq { background: #F44336; }
        .icon-fill-blank { background: #3F51B5; }
        .icon-true-false { background: #009688; }
        .icon-match { background: #E91E63; }
        .icon-sequence { background: #FF5722; }

        /* Content Sections */
        .content-section {
            margin-bottom: 40px;
        }

        .section-header {
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .section-header h2 {
            color: #333;
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .section-description {
            color: #666;
            font-size: 1rem;
            margin: 0;
        }

        /* Question Styles */
        .question-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .question-item:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-color: #667eea;
        }

        .question-text {
            font-size: 1.1rem;
            font-weight: 500;
            color: #333;
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .answer-text {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            font-size: 1rem;
            line-height: 1.6;
        }

        .question-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-explanation {
            background: #17a2b8;
            color: white;
        }

        .btn-explanation:hover {
            background: #138496;
            transform: translateY(-1px);
        }

        .btn-create-more {
            background: #28a745;
            color: white;
        }

        .btn-create-more:hover {
            background: #218838;
            transform: translateY(-1px);
        }

        .btn-ask-doubt {
            background: #ffc107;
            color: #212529;
        }

        .btn-ask-doubt:hover {
            background: #e0a800;
            transform: translateY(-1px);
        }

        /* Explanation Styles */
        .explanation-container {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
            display: none;
            animation: slideDown 0.3s ease;
        }

        .explanation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .explanation-title {
            font-weight: 600;
            color: #0c5460;
            margin: 0;
        }

        .close-explanation {
            background: none;
            border: none;
            color: #0c5460;
            cursor: pointer;
            font-size: 1.2rem;
        }

        .explanation-text {
            color: #0c5460;
            line-height: 1.6;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
            }
            to {
                opacity: 1;
                max-height: 500px;
            }
        }

        /* Question Type Sections */
        .question-type-section {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            overflow: hidden;
        }

        .question-type-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .question-type-header:hover {
            background: linear-gradient(135deg, #5a6fd8, #6a4190);
        }

        .question-type-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin: 0;
        }

        .question-type-count {
            background: rgba(255,255,255,0.2);
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.9rem;
        }

        .question-type-content {
            padding: 25px;
            display: none;
        }

        .question-type-content.open {
            display: block;
        }

        .toggle-icon {
            transition: transform 0.3s ease;
        }

        .toggle-icon.rotated {
            transform: rotate(180deg);
        }

        /* MCQ Specific Styles */
        .mcq-options {
            margin: 15px 0;
        }

        .mcq-option {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px 15px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .mcq-option.correct {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .option-label {
            font-weight: 600;
            min-width: 25px;
        }

        /* MCQ Action Buttons */
        .mcq-actions {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .mcq-action-btn {
            padding: 8px 16px;
            border: 1px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .mcq-action-btn:hover {
            background: #667eea;
            color: white;
        }

        /* Toast Notification */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 1000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .toast.show {
            opacity: 1;
            transform: translateX(0);
        }

        /* No Data Message */
        .no-data-message {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .no-data-icon {
            font-size: 3rem;
            color: #ccc;
            margin-bottom: 15px;
        }

        /* Go to Top Button */
        .go-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
            z-index: 1000;
        }

        .go-to-top.visible {
            opacity: 1;
            visibility: visible;
        }

        .go-to-top:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .book-title {
                font-size: 2rem;
            }

            .ebook-container {
                padding: 15px;
            }

            .question-type-nav {
                grid-template-columns: 1fr;
            }

            .question-actions {
                flex-direction: column;
            }

            .action-btn {
                text-align: center;
                justify-content: center;
            }

            .chapter-dropdown {
                max-width: 100%;
            }

            .type-nav-item {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .go-to-top {
                bottom: 20px;
                right: 20px;
                width: 45px;
                height: 45px;
                font-size: 1.1rem;
            }
        }
    </style>
</head>
<body>
    <script>
        var loggedIn = false;
        var currentChapterId = null;
        var chapterData = {};
    </script>

    <sec:ifLoggedIn>
        <script>
            loggedIn = true;
        </script>
    </sec:ifLoggedIn>
    <div class="ebook-container">
        <!-- Header Section -->
        <div class="ebook-header">
            <h1 class="book-title">${booksMst.title}</h1>
            <p class="book-subtitle">Interactive AI-Enabled Learning Experience</p>
        </div>

        <!-- Main Layout -->
        <div class="ebook-layout">
            <!-- Chapter Selection -->
            <div class="chapter-selection">
                <h3>Select Chapter</h3>
                <select class="chapter-dropdown" id="chapterDropdown" onchange="loadChapterFromDropdown(this.value)">
                    <option value="">-- Select a chapter --</option>
                    <g:each in="${chaptersList}" var="chapter" status="index">
                        <option value="${chapter.chapterId}" ${index == 0 ? 'selected' : ''}>
                            ${chapter.chapterName}
                        </option>
                    </g:each>
                </select>
            </div>

            <!-- Content Area -->
            <div class="content-area">
                <!-- Loading Spinner -->
                <div class="loading-spinner" id="loadingSpinner">
                    <div class="spinner"></div>
                    <p>Loading chapter content...</p>
                </div>

                <!-- Chapter Summary -->
                <div class="chapter-summary" id="chapterSummary" style="display: none;">
                    <h2 class="summary-title">
                        <i class="fas fa-chart-bar"></i> Chapter Overview
                    </h2>
                    <div class="question-type-nav" id="questionTypeNav">
                        <!-- Question type navigation will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Exercise Solutions Section -->
                <div class="content-section" id="exerciseSolutionsSection" style="display: none;">
                    <div class="section-header">
                        <h2><i class="fas fa-pencil-alt"></i> Exercise Solutions</h2>
                        <p class="section-description">Practice problems with detailed solutions and explanations</p>
                    </div>
                    <div class="questions-container" id="exerciseSolutionsContainer">
                        <!-- Exercise solutions will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Question Bank Section -->
                <div class="content-section" id="questionBankSection" style="display: none;">
                    <div class="section-header">
                        <h2><i class="fas fa-database"></i> Question Bank</h2>
                        <p class="section-description">Comprehensive collection of practice questions by type</p>
                    </div>
                    <div class="question-types-container" id="questionTypesContainer">
                        <!-- Question types will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Welcome Message -->
                <div class="welcome-message" id="welcomeMessage">
                    <div style="text-align: center; padding: 60px 20px;">
                        <i class="fas fa-book-reader" style="font-size: 4rem; color: #667eea; margin-bottom: 20px;"></i>
                        <h2 style="color: #333; margin-bottom: 15px;">Welcome to Your AI-Enabled Ebook</h2>
                        <p style="color: #666; font-size: 1.1rem; max-width: 600px; margin: 0 auto;">
                            Select a chapter from the navigation to start exploring interactive content,
                            practice questions, and detailed explanations powered by AI.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Go to Top Button -->
    <button class="go-to-top" id="goToTopBtn" onclick="scrollToTop()">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Toast Notification -->
    <div class="toast" id="toast"></div>

    <!-- JavaScript -->
    <script>
        // Global variables
        var currentChapterId = null;
        var chapterData = {};

        // Question type mappings
        var questionTypeMap = {
            "LongAnswer": { title: "Long Answer Type", icon: "fas fa-align-left" },
            "ShortAnswer": { title: "Short Answer Type", icon: "fas fa-align-center" },
            "VeryShortAnswer": { title: "Very Short Answer Type", icon: "fas fa-align-justify" },
            "AssertionReason": { title: "Assertion / Reasoning Type", icon: "fas fa-balance-scale" },
            "Problem": { title: "Problem", icon: "fas fa-calculator" },
            "Multiple Choice Questions": { title: "Multiple Choice Questions", icon: "fas fa-list-ul" },
            "FillBlank": { title: "Fill in the Blanks", icon: "fas fa-edit" },
            "TrueFalse": { title: "True or False", icon: "fas fa-check-circle" },
            "MatchFollowing": { title: "Match the Following", icon: "fas fa-link" },
            "ArrangeSequence": { title: "Arrange in Right Sequence", icon: "fas fa-sort-numeric-down" }
        };

        // Initialize on page load
        document.addEventListener("DOMContentLoaded", function() {
            // Load first chapter by default if available
            var firstChapterLink = document.querySelector(".chapter-link");
            if (firstChapterLink) {
                var chapterId = firstChapterLink.getAttribute("data-chapter-id");
                loadChapter(chapterId, firstChapterLink);
            }
        });

        // Mobile menu toggle
        function toggleMobileMenu() {
            var nav = document.getElementById("chapterNav");
            nav.classList.toggle("mobile-open");
        }

        // Load chapter content
        function loadChapter(chapterId, linkElement) {
            // Update active chapter
            document.querySelectorAll(".chapter-link").forEach(function(link) {
                link.classList.remove("active");
            });
            linkElement.classList.add("active");

            // Close mobile menu
            document.getElementById("chapterNav").classList.remove("mobile-open");

            // Show loading spinner
            showLoading();

            // Hide all content sections
            hideAllSections();

            currentChapterId = chapterId;

            // Fetch chapter data
            fetch("/wpmain/getChapterContent?chapterId=" + chapterId)
                .then(function(response) {
                    return response.json();
                })
                .then(function(data) {
                    chapterData[chapterId] = data;
                    displayChapterContent(data);
                    hideLoading();
                })
                .catch(function(error) {
                    console.error("Error loading chapter:", error);
                    showToast("Error loading chapter content");
                    hideLoading();
                });
        }

        // Display chapter content
        function displayChapterContent(data) {
            displayChapterSummary(data.questionTypeCounts);
            displayExerciseSolutions(data.exerciseSolutions);
            displayQuestionBank(data.questionBank);

            // Show content sections
            document.getElementById("chapterSummary").style.display = "block";
            document.getElementById("exerciseSolutionsSection").style.display = "block";
            document.getElementById("questionBankSection").style.display = "block";

            // Render math formulas
            renderMathFormulas();
        }

        // Display chapter summary
        function displayChapterSummary(questionTypeCounts) {
            var navContainer = document.getElementById("questionTypeNav");
            var html = "";

            // Add Exercise Solutions
            var exerciseCount = chapterData[currentChapterId].exerciseSolutions.length;
            if (exerciseCount > 0) {
                html += "<div class=\"type-nav-item\" onclick=\"scrollToSection('exerciseSolutionsSection')\">" +
                       "<div class=\"type-nav-icon\"><i class=\"fas fa-pencil-alt\"></i></div>" +
                       "<div class=\"type-nav-title\">Exercise Solutions</div>" +
                       "<div class=\"type-nav-count\">" + exerciseCount + " questions</div>" +
                       "</div>";
            }

            // Add question types
            Object.keys(questionTypeCounts).forEach(function(qType) {
                var typeInfo = questionTypeMap[qType];
                if (typeInfo) {
                    html += "<div class=\"type-nav-item\" onclick=\"scrollToQuestionType('" + qType + "')\">" +
                           "<div class=\"type-nav-icon\"><i class=\"" + typeInfo.icon + "\"></i></div>" +
                           "<div class=\"type-nav-title\">" + typeInfo.title + "</div>" +
                           "<div class=\"type-nav-count\">" + questionTypeCounts[qType] + " questions</div>" +
                           "</div>";
                }
            });

            if (html === "") {
                html = "<div class=\"no-data-message\">" +
                      "<div class=\"no-data-icon\"><i class=\"fas fa-info-circle\"></i></div>" +
                      "<p>No content available for this chapter.</p>" +
                      "</div>";
            }

            navContainer.innerHTML = html;
        }

        // Display exercise solutions
        function displayExerciseSolutions(exerciseSolutions) {
            var container = document.getElementById("exerciseSolutionsContainer");
            var html = "";

            if (exerciseSolutions.length === 0) {
                html = "<div class=\"no-data-message\">" +
                      "<div class=\"no-data-icon\"><i class=\"fas fa-info-circle\"></i></div>" +
                      "<p>No exercise solutions available for this chapter.</p>" +
                      "</div>";
            } else {
                exerciseSolutions.forEach(function(question, index) {
                    html += "<div class=\"question-item\">" +
                           "<div class=\"question-text\">" + question.question + "</div>" +
                           "<div class=\"answer-text\">" + question.answer + "</div>" +
                           "<div class=\"question-actions\">" +
                           "<button class=\"action-btn btn-explanation\" onclick=\"toggleExplanation(" + question.id + ", this)\">" +
                           "<i class=\"fas fa-lightbulb\"></i> Show Explanation" +
                           "</button>" +
                           "<button class=\"action-btn btn-create-more\" onclick=\"createMore(" + question.id + ")\">" +
                           "<i class=\"fas fa-plus\"></i> Create More" +
                           "</button>" +
                           "<button class=\"action-btn btn-ask-doubt\" onclick=\"askDoubt(" + question.id + ")\">" +
                           "<i class=\"fas fa-question-circle\"></i> Ask Doubt" +
                           "</button>" +
                           "</div>" +
                           "<div class=\"explanation-container\" id=\"explanation-" + question.id + "\">" +
                           "<div class=\"explanation-header\">" +
                           "<h4 class=\"explanation-title\"><i class=\"fas fa-info-circle\"></i> Explanation</h4>" +
                           "<button class=\"close-explanation\" onclick=\"closeExplanation(" + question.id + ")\">" +
                           "<i class=\"fas fa-times\"></i>" +
                           "</button>" +
                           "</div>" +
                           "<div class=\"explanation-text\" id=\"explanation-text-" + question.id + "\"></div>" +
                           "</div>" +
                           "</div>";
                });
            }

            container.innerHTML = html;
        }

        // Display question bank
        function displayQuestionBank(questionBank) {
            var container = document.getElementById("questionTypesContainer");
            var html = "";

            // Process QnA questions by type
            if (questionBank.qnaQuestions) {
                Object.keys(questionBank.qnaQuestions).forEach(function(qType) {
                    var questions = questionBank.qnaQuestions[qType];
                    var typeInfo = questionTypeMap[qType];
                    if (typeInfo && questions.length > 0) {
                        html += createQuestionTypeSection(qType, typeInfo, questions);
                    }
                });
            }

            // Process MCQ questions
            if (questionBank.mcqQuestions && questionBank.mcqQuestions.length > 0) {
                var mcqTypeInfo = questionTypeMap["Multiple Choice Questions"];
                html += createMCQSection(mcqTypeInfo, questionBank.mcqQuestions);
            }

            if (html === "") {
                html = "<div class=\"no-data-message\">" +
                      "<div class=\"no-data-icon\"><i class=\"fas fa-info-circle\"></i></div>" +
                      "<p>No question bank content available for this chapter.</p>" +
                      "</div>";
            }

            container.innerHTML = html;
        }

        // Create question type section
        function createQuestionTypeSection(qType, typeInfo, questions) {
            var sectionId = "section-" + qType.replace(/\s+/g, "-");
            var html = "<div class=\"question-type-section\" id=\"" + sectionId + "\">" +
                      "<div class=\"question-type-header\" onclick=\"toggleQuestionType('" + sectionId + "')\">" +
                      "<div>" +
                      "<i class=\"" + typeInfo.icon + "\"></i> " +
                      "<span class=\"question-type-title\">" + typeInfo.title + "</span>" +
                      "</div>" +
                      "<div>" +
                      "<span class=\"question-type-count\">" + questions.length + " questions</span>" +
                      "<i class=\"fas fa-chevron-down toggle-icon\" id=\"toggle-" + sectionId + "\"></i>" +
                      "</div>" +
                      "</div>" +
                      "<div class=\"question-type-content\" id=\"content-" + sectionId + "\">";

            questions.forEach(function(question) {
                html += "<div class=\"question-item\">" +
                       "<div class=\"question-text\">" + question.question + "</div>" +
                       "<div class=\"answer-text\">" + question.answer + "</div>" +
                       "<div class=\"question-actions\">" +
                       "<button class=\"action-btn btn-explanation\" onclick=\"toggleExplanation(" + question.id + ", this)\">" +
                       "<i class=\"fas fa-lightbulb\"></i> Show Explanation" +
                       "</button>" +
                       "<button class=\"action-btn btn-create-more\" onclick=\"createMore(" + question.id + ")\">" +
                       "<i class=\"fas fa-plus\"></i> Create More" +
                       "</button>" +
                       "<button class=\"action-btn btn-ask-doubt\" onclick=\"askDoubt(" + question.id + ")\">" +
                       "<i class=\"fas fa-question-circle\"></i> Ask Doubt" +
                       "</button>" +
                       "</div>" +
                       "<div class=\"explanation-container\" id=\"explanation-" + question.id + "\">" +
                       "<div class=\"explanation-header\">" +
                       "<h4 class=\"explanation-title\"><i class=\"fas fa-info-circle\"></i> Explanation</h4>" +
                       "<button class=\"close-explanation\" onclick=\"closeExplanation(" + question.id + ")\">" +
                       "<i class=\"fas fa-times\"></i>" +
                       "</button>" +
                       "</div>" +
                       "<div class=\"explanation-text\" id=\"explanation-text-" + question.id + "\"></div>" +
                       "</div>" +
                       "</div>";
            });

            html += "</div></div>";
            return html;
        }

        // Create MCQ section
        function createMCQSection(typeInfo, questions) {
            var sectionId = "section-mcq";
            var html = "<div class=\"question-type-section\" id=\"" + sectionId + "\">" +
                      "<div class=\"question-type-header\" onclick=\"toggleQuestionType('" + sectionId + "')\">" +
                      "<div>" +
                      "<i class=\"" + typeInfo.icon + "\"></i> " +
                      "<span class=\"question-type-title\">" + typeInfo.title + "</span>" +
                      "</div>" +
                      "<div>" +
                      "<span class=\"question-type-count\">" + questions.length + " questions</span>" +
                      "<div class=\"mcq-actions\">" +
                      "<button class=\"mcq-action-btn\" onclick=\"playMCQ()\">" +
                      "<i class=\"fas fa-play\"></i> Play" +
                      "</button>" +
                      "<button class=\"mcq-action-btn\" onclick=\"practiceMCQ()\">" +
                      "<i class=\"fas fa-dumbbell\"></i> Practice" +
                      "</button>" +
                      "<button class=\"mcq-action-btn\" onclick=\"testMCQ()\">" +
                      "<i class=\"fas fa-clipboard-check\"></i> Test" +
                      "</button>" +
                      "</div>" +
                      "<i class=\"fas fa-chevron-down toggle-icon\" id=\"toggle-" + sectionId + "\"></i>" +
                      "</div>" +
                      "</div>" +
                      "<div class=\"question-type-content\" id=\"content-" + sectionId + "\">";

            questions.forEach(function(question) {
                html += "<div class=\"question-item\">" +
                       "<div class=\"question-text\">" + question.question + "</div>";

                // Add MCQ options
                if (question.option1 || question.option2 || question.option3 || question.option4 || question.option5) {
                    html += "<div class=\"mcq-options\">";
                    if (question.option1) {
                        var isCorrect = question.answer === "1" ? " correct" : "";
                        html += "<div class=\"mcq-option" + isCorrect + "\">" +
                               "<span class=\"option-label\">A.</span>" + question.option1 + "</div>";
                    }
                    if (question.option2) {
                        var isCorrect = question.answer === "2" ? " correct" : "";
                        html += "<div class=\"mcq-option" + isCorrect + "\">" +
                               "<span class=\"option-label\">B.</span>" + question.option2 + "</div>";
                    }
                    if (question.option3) {
                        var isCorrect = question.answer === "3" ? " correct" : "";
                        html += "<div class=\"mcq-option" + isCorrect + "\">" +
                               "<span class=\"option-label\">C.</span>" + question.option3 + "</div>";
                    }
                    if (question.option4) {
                        var isCorrect = question.answer === "4" ? " correct" : "";
                        html += "<div class=\"mcq-option" + isCorrect + "\">" +
                               "<span class=\"option-label\">D.</span>" + question.option4 + "</div>";
                    }
                    if (question.option5) {
                        var isCorrect = question.answer === "5" ? " correct" : "";
                        html += "<div class=\"mcq-option" + isCorrect + "\">" +
                               "<span class=\"option-label\">E.</span>" + question.option5 + "</div>";
                    }
                    html += "</div>";
                }

                html += "<div class=\"question-actions\">" +
                       "<button class=\"action-btn btn-explanation\" onclick=\"toggleExplanation(" + question.id + ", this)\">" +
                       "<i class=\"fas fa-lightbulb\"></i> Show Explanation" +
                       "</button>" +
                       "<button class=\"action-btn btn-create-more\" onclick=\"createMore(" + question.id + ")\">" +
                       "<i class=\"fas fa-plus\"></i> Create More" +
                       "</button>" +
                       "<button class=\"action-btn btn-ask-doubt\" onclick=\"askDoubt(" + question.id + ")\">" +
                       "<i class=\"fas fa-question-circle\"></i> Ask Doubt" +
                       "</button>" +
                       "</div>" +
                       "<div class=\"explanation-container\" id=\"explanation-" + question.id + "\">" +
                       "<div class=\"explanation-header\">" +
                       "<h4 class=\"explanation-title\"><i class=\"fas fa-info-circle\"></i> Explanation</h4>" +
                       "<button class=\"close-explanation\" onclick=\"closeExplanation(" + question.id + ")\">" +
                       "<i class=\"fas fa-times\"></i>" +
                       "</button>" +
                       "</div>" +
                       "<div class=\"explanation-text\" id=\"explanation-text-" + question.id + "\"></div>" +
                       "</div>" +
                       "</div>";
            });

            html += "</div></div>";
            return html;
        }

        // Toggle question type section
        function toggleQuestionType(sectionId) {
            var content = document.getElementById("content-" + sectionId);
            var toggle = document.getElementById("toggle-" + sectionId);

            if (content.classList.contains("open")) {
                content.classList.remove("open");
                content.style.display = "none";
                toggle.classList.remove("rotated");
            } else {
                content.classList.add("open");
                content.style.display = "block";
                toggle.classList.add("rotated");

                // Render math formulas in the newly opened section
                setTimeout(function() {
                    renderMathFormulas();
                }, 100);
            }
        }

        // Toggle explanation
        function toggleExplanation(questionId, buttonElement) {
            var explanationContainer = document.getElementById("explanation-" + questionId);
            var explanationText = document.getElementById("explanation-text-" + questionId);

            if (explanationContainer.style.display === "block") {
                explanationContainer.style.display = "none";
                buttonElement.innerHTML = "<i class=\"fas fa-lightbulb\"></i> Show Explanation";
            } else {
                // Show loading spinner
                explanationText.innerHTML = "<div style=\"text-align: center; padding: 20px;\">" +
                                          "<i class=\"fas fa-spinner fa-spin\"></i> Loading explanation..." +
                                          "</div>";
                explanationContainer.style.display = "block";
                buttonElement.innerHTML = "<i class=\"fas fa-eye-slash\"></i> Hide Explanation";

                // Fetch explanation
                fetch("/wpmain/getExplanation?questionId=" + questionId)
                    .then(function(response) {
                        return response.json();
                    })
                    .then(function(data) {
                        explanationText.innerHTML = data.explanation || "No explanation available.";

                        // Scroll to explanation
                        explanationContainer.scrollIntoView({
                            behavior: "smooth",
                            block: "nearest"
                        });

                        // Render math formulas
                        renderMathFormulas();
                    })
                    .catch(function(error) {
                        console.error("Error loading explanation:", error);
                        explanationText.innerHTML = "Error loading explanation.";
                    });
            }
        }

        // Close explanation
        function closeExplanation(questionId) {
            var explanationContainer = document.getElementById("explanation-" + questionId);
            var button = document.querySelector("[onclick*=\"toggleExplanation(" + questionId + "\"]");

            explanationContainer.style.display = "none";
            if (button) {
                button.innerHTML = "<i class=\"fas fa-lightbulb\"></i> Show Explanation";
            }
        }

        // Utility functions
        function showLoading() {
            document.getElementById("loadingSpinner").style.display = "block";
        }

        function hideLoading() {
            document.getElementById("loadingSpinner").style.display = "none";
        }

        function hideAllSections() {
            document.getElementById("welcomeMessage").style.display = "none";
            document.getElementById("chapterSummary").style.display = "none";
            document.getElementById("exerciseSolutionsSection").style.display = "none";
            document.getElementById("questionBankSection").style.display = "none";
        }

        function scrollToSection(sectionId) {
            var element = document.getElementById(sectionId);
            if (element) {
                element.scrollIntoView({ behavior: "smooth", block: "start" });
            }
        }

        function scrollToQuestionType(qType) {
            var sectionId = "section-" + qType.replace(/\s+/g, "-");
            var element = document.getElementById(sectionId);
            if (element) {
                // Open the section if it's closed
                var content = document.getElementById("content-" + sectionId);
                var toggle = document.getElementById("toggle-" + sectionId);
                if (!content.classList.contains("open")) {
                    toggleQuestionType(sectionId);
                }

                // Scroll to the section
                element.scrollIntoView({ behavior: "smooth", block: "start" });
            }
        }

        function showToast(message) {
            var toast = document.getElementById("toast");
            toast.textContent = message;
            toast.classList.add("show");

            setTimeout(function() {
                toast.classList.remove("show");
            }, 3000);
        }

        // Placeholder functions for future implementation
        function createMore(questionId) {
            showToast("Create More feature coming soon!");
        }

        function askDoubt(questionId) {
            showToast("Ask Doubt feature coming soon!");
        }

        function playMCQ() {
            showToast("Play MCQ feature coming soon!");
        }

        function practiceMCQ() {
            showToast("Practice MCQ feature coming soon!");
        }

        function testMCQ() {
            showToast("Test MCQ feature coming soon!");
        }

        // Math formula rendering
        function renderMathFormulas() {
            if (typeof renderMathInElement === "function") {
                var contentArea = document.querySelector(".content-area");
                renderMathInElement(contentArea, {
                    delimiters: [
                        {left: "$$", right: "$$", display: true},
                        {left: "$", right: "$", display: false},
                        {left: "\\(", right: "\\)", display: false},
                        {left: "\\[", right: "\\]", display: true}
                    ]
                });
            } else if (typeof MathJax !== "undefined" && MathJax.Hub && MathJax.Hub.Queue) {
                MathJax.Hub.Queue(["Typeset", MathJax.Hub, document.querySelector(".content-area")]);
            }
        }
    </script>

    <g:render template="/${session['entryController']}/footer_new"></g:render>
</body>
</html>
