<!DOCTYPE html>
<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/privatelabel/navheader_new"></g:render>
<% if(!session['wileySite']){%>
<style>
    .newArrivals{
        margin-bottom: 0 !important;
        margin-top: 1rem !important;
    }
    #newReleaseBooks{
        padding-bottom: 8px;
    }
    .new-arrival{
        width: 200px;
        box-shadow: 0px 3px 3px rgba(0,0,0,0.15);
    }
    .product-image{
        min-height: fit-content;
        max-height: 185px !important;
    }
    .product-name{
        font-size: 15px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        line-height: normal;
    }
    .publisher-name{
        font-size: 13px;
    }
    .categories-section{
        padding-top: 10px !important;
    }
    .listPriceText{
        text-decoration: line-through;
        color: #888;
    }
    .offerPriceText{
        font-weight: 600;
        margin-right: 6px;
    }
    .offerPercentage{
        margin-left: 6px;
        color: red;
    }
    .newArrivals_title{
        margin-bottom: 1.2rem;
    }
    .newArrivals_title h4{
        position: relative;
    }
    .newArrivals_title h4:after {
        width: 50px;
        height: 2px;
        content: '';
        position: absolute;
        background: #C9302C;
        left: 0;
        bottom: -4px;
    }
    .unknownCoverImg{
        border-radius: 5px;
        color: #fff;
        padding: 15px;
        position: relative;
        width: 175px;
        height: 185px;
    }
    .unknownCoverImg::after{
        content: '';
        position: absolute;
        top: 0;
        left: 5px;
        bottom: 0;
        width: 2px;
        background: #0000001A;
        box-shadow: 1px 0 3px rgba(255, 255, 255, 0.3);
    }
</style>
<%}%>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<div id="wrapper" class="index-page">

    <div class="header-wrapper">
        <div class="bg-wrapper-sec">

            <div id="slider-desktop" class="carousel slide this-is-a-web-view-slider" data-ride="carousel">
                <ol class="carousel-indicators" id="slider-desktop-indicators"></ol>
                <div class="carousel-inner" id="slider-desktop-views"></div>
            </div>

            <div id="bindAllItem">
                <div id="slider-mobile" class="carousel slide this-is-a-responsive-view-slider" data-ride="carousel">
                    <ol class="carousel-indicators" id="slider-mobile-indicators"></ol>
                    <div class="carousel-inner" id="slider-mobile-views"></div>
                </div>
            </div>

        </div>
    </div>


    <div class="newArrivals container d-none">
        <div class="newArrivals_title">
            <% if(session['wileySite']){%>
            <h2 class="text-center">New Arrivals</h2>
            <%} else {%>
            <h4 class="">Newly Released Books</h4>
            <%}%>
        </div>

        <div class="newArrivals_wrapper">
            <div class="scrollable-container">
                <div class="scrollable-content" id="newReleaseBooks">


                </div>
                <div class="scroll-buttons d-none">
                    <button class="scroll-button left">&lt;</button>
                    <button class="scroll-button right">&gt;</button>
                </div>
            </div>

        </div>
    </div>

    <% if(!session['wileySite']){%>
    <div class="categories-section">
        <div class="container">
            <div class="row justify-content-center d-none">
                <div class="col-md-10 col-lg-7 mb-5">
                    <div class="text-center">
                        <h2 class="title-categories-section d-none">Book Categories</h2>
                    </div>
                </div>
            </div>

            <div id="ebookCategories">
                <div class="category_list">

                </div>
            </div>
            <%if(productYoutubeLink!=null && productYoutubeLink!=""){%>
            <div class="video-section">
                <h2>${youtubeLinkTitle}</h2>
                <div class="embed-responsive embed-responsive-16by9">
                    <iframe style="border-radius: 30px" class="embed-responsive-item" src="${productYoutubeLink}?controls=1" allowfullscreen></iframe>
                </div>
            </div>
            <%}%>
        </div>
    </div>
    <% if(!"55".equals(""+session['siteId'])){ %>
    <div class="connect-section">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-2 display-none-responsive">
                </div>
                <div class="col-lg-8">
                    <div class="row justify-content-center">
                        <div class="col-lg-9 col-sm-10">
                            <div class="text-center border-title-coonect">
                                <h2 class="title-connect-section">Connect</h2>
                            </div>
                        </div>
                        <div class="col-lg-9 col-sm-9">
                            <p class="connect-sec-wrp-disc-pera">'Customer Experience always comes first', we genuinely believe in this and would  always love to hear from you, be it related to books, orders, content or business. <br/> Get in touch and follow us to stay updated.</p>
                        </div>
                    </div>

                    <div class="row mrgn-top-connect-sec">
                        <div class="col-lg-6 col-sm-12 center-responsive-div">
                            <ul class="social-icon-wrp-connect">
                                <% if(session["facebookLink"]) { %>
                                <li><a href="${session["facebookLink"]}" target="_blank"><i class="fa fa-facebook"></i></a></li>
                                <% } %>
                                <% if(session["twitterLink"]) { %>
                                <li><a href="${session["twitterLink"]}" target="_blank"><i class="fa fa-twitter"></i></a></li>
                                <% } %>
                                <% if(session["instagramLink"]) { %>
                                <li><a href="${session["instagramLink"]}" target="_blank"><i class="fa fa-instagram"></i></a></li>
                                <% } %>
                                <% if(session["linkedinLink"]) { %>
                                <li><a href="${session["linkedinLink"]}" target="_blank"><i class="fa fa-linkedin"></i></a></li>
                                <% } %>
                                <% if(session["youtubeLink"]) { %>
                                <li><a href="${session["youtubeLink"]}" target="_blank"><i class="fa fa-youtube-play"></i></a></li>
                                <% } %>
                            </ul>
                        </div>

                        <div class="col-lg-6 col-sm-12 responsive-center-text">
                            <h3 class="call-here-number"><a href="tel:${session["mobileNumber"]}">${session["mobileNumber"]}</a></h3>
                        </div>

                    </div>
                </div>

            </div>
        </div>
    </div>
    <%}%>
    <%}%>

</div>

<script>
    var base_url = location.protocol + '//' + location.host;
    window.history.replaceState("", "", base_url);
    var activeCategories = JSON.parse("${session["activeCategories_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));
    var activeCategoriesSyllabus = JSON.parse("${session["activeCategoriesSyllabus_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));
    const groupedData = activeCategoriesSyllabus.reduce((acc, obj) => {
        const key = obj.level;
        if (!acc[key]) {
            acc[key] = [];
        }
        acc[key].push(obj);
        return acc;
    }, {});

    let html = '';

    for (const key in groupedData) {
        const group = groupedData[key];
        html += "<div class='category_level'>"+
            "<h4><a href='/sp/${session["siteName"]}/store?level="+encodeURIComponent(key)+"' style='font-size:inherit;color: inherit;font-weight: inherit;'>"+
            key+"</a></h4>";
        html += "<div class='category_cards'>";
        for (const obj of group) {
            html +="<a href='/sp/${session["siteName"]}/store?level="+encodeURIComponent(key)+"&syllabus="+encodeURIComponent(obj.syllabus)+"' class='category_card'>"+
                "<div class='category_card-title'>"+
                "<p>"+obj.syllabus+"</p>"+
                "</div>"+
                "</a>";
        }
        html += '</div>'+
            "</div>";
    }

 /**   html += "<div class='category_level'>"+
        "<h4>Subjects</h4>";
    html += "<div class='category_cards'>";
    var activeSubjects = JSON.parse("${session["activeSubjects_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));
    var subject = [];
    for(var i=0;i<activeSubjects.length;i++) {
        if (subject.indexOf(activeSubjects[i].subject) == -1) {
            subject.push(activeSubjects[i].subject);
            html += "<a href='/sp/${session["siteName"]}/store?subject=" + encodeURIComponent(activeSubjects[i].subject) + "' class='category_card'>" +
                "<div class='category_card-title'>" +
                "<p>" + activeSubjects[i].subject + "</p>" +
                "</div>" +
                "</a>";
        }
    }*/
    html += '</div>'+
        "</div>";
    if (document.querySelector(".category_list")){
        document.querySelector(".category_list").innerHTML=html;
    }
    function logout(){
        setCookie("level","");
        setCookie("syllabus","");
        setCookie("grade","");
        setCookie("subject","");
        window.location.href = '/logoff';
    }
</script>

<g:render template="/privatelabel/footer_new"></g:render>

<script>
    var siteId = "${session["siteId"]}";
    var privateLabelBanner = "${session["bannerImage"]}";
    var checkDescBanners = false;
    var checkMobBanners = false;
    var templateDesktop = "";
    var templateMobile = "";
    var defaultDeskImageUrl = "/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["bannerImage"]}&imgType=webp";
    var defaultMobImageUrl = "/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["bannerImage"]}&imgType=webp";

    function showBanners(data) {
        setTimeout(function () {
            $(".loading-icon").addClass("hidden");
        },2000);
        if(data.status=="OK") {
            var banners = data.banners.reverse();
            $('#slider-desktop-views,#slider-mobile-views').empty();

            // Banner slider
            $.each(banners, function (i, v) {
                var item = v;
                var htmlStr = '';
                var htmlStrMobile = '';
                var indicatorStr = '';
                var indicatorStrMobile = '';
                var imageDescUrl = "/wonderpublish/showImage?id=" + v.id + "&fileName=" + v.imagePath + "&imgType=webp";
                var imageMobUrl = "/wonderpublish/showImage?id="+v.id+"&fileName="+v.imagePathMobile + "&imgType=webp";
                if(v.bookTitle) {
                    var bookTitle = "/" + replaceAll(replaceAll(v.bookTitle, ' ', '-').toLowerCase(), '\'', '');
                }
                var bookHyperLink = bookTitle + "/ebook-details?siteName=${session['entryController']}&bookId=" + v.bookId + "&preview=true";

                var actionLink = v.action //action field link
                var serverURL =  window.location.origin //getting base url
                actionLink = serverURL+'/'+actionLink;

                indicatorStr += "<li data-target='#slider-desktop' class='border-light' data-slide-to='" + i + "'></li>";
                indicatorStrMobile += "<li data-target='#slider-mobile' class='border-light' data-slide-to='" + i + "'></li>";
                if(v.bookId) {
                    htmlStr += '<div class="carousel-item">' +
                        '<a href="' + bookHyperLink + '" target="_blank" class="d-block">' +
                        '<img src="'+imageDescUrl+'">' +
                        '</a>' +
                        '</div>';
                    htmlStrMobile += '<div class="carousel-item">' +
                        '<a href="' + bookHyperLink + '" target="_blank" class="d-block">' +
                        '<img src="'+imageMobUrl+'">' +
                        '</a>' +
                        '</div>';
                } else if (v.action) {
                    htmlStr += '<div class="carousel-item">' +
                        '<a href="' + actionLink + '" target="_blank" class="d-block">' +
                        '<img src="'+imageDescUrl+'">' +
                        '</a>' +
                        '</div>';
                    htmlStrMobile += '<div class="carousel-item">' +
                        '<a href="' + actionLink + '" target="_blank" class="d-block">' +
                        '<img src="'+imageMobUrl+'">' +
                        '</a>' +
                        '</div>';
                }else{
                    htmlStr += '<div class="carousel-item">' +
                        '<img src="'+imageDescUrl+'">' +
                        '</div>';
                    htmlStrMobile += '<div class="carousel-item">' +
                        '<img src="'+imageMobUrl+'">' +
                        '</div>';
                }

                // If desktop banners are available
                if(v.imagePath) {
                    checkDescBanners = true;
                    $('#slider-desktop-views').append(htmlStr).find('.carousel-item:first-child').addClass('active');
                    $('#slider-desktop-indicators').append(indicatorStr).find('li:first-child').addClass('active');
                }

                // If mobile banners are available
                if(v.imagePathMobile) {
                    checkMobBanners = true;
                    $('#slider-mobile-views').append(htmlStrMobile).find('.carousel-item:first-child').addClass('active');
                    $('#slider-mobile-indicators').append(indicatorStrMobile).find('li:first-child').addClass('active');
                }

            });

        } else if(data.status=="Nothing Present") {
            checkDescBanners = false; checkMobBanners = false;
        }

        // Showing empty banners based on condition
        if(!checkDescBanners && !checkMobBanners) {
            if (privateLabelBanner == "") {
                document.querySelector(".header-wrapper").setAttribute('style', 'display: none;');
            } else {
                templateDesktop += emptyDesktopBannerUI(defaultDeskImageUrl);
                $('#slider-desktop-views').append(templateDesktop);
                templateMobile += emptyMobileBannerUI(defaultMobImageUrl);
                $('#slider-mobile-views').append(templateMobile);
            }
        } else if(!checkMobBanners) {
            if (window.matchMedia('(max-width: 767px)').matches) {
                if (privateLabelBanner == "") {
                    document.querySelector(".header-wrapper").setAttribute('style', 'display: none;');
                } else {
                    templateMobile += emptyMobileBannerUI(defaultMobImageUrl);
                    $('#slider-mobile-views').append(templateMobile);
                }
            }
        }

    }

    // If desktop banner images are empty calling this function
    function emptyDesktopBannerUI(defaultImage) {
        var emptyBannerDesk = '<div class="carousel-item active">' +
            '<img src="'+defaultImage+'">' +
            '</div>';
        return emptyBannerDesk;
    }

    // If mobile banner images are empty calling this function
    function emptyMobileBannerUI(defaultImage) {
        var emptyBannerMob = '<div class="carousel-item active">' +
            '<img src="'+defaultImage+'">' +
            '</div>';
        return emptyBannerMob;
    }

    function getBannerDetails(siteId) {
        $(".loading-icon").removeClass("hidden");
        <g:remoteFunction controller="wonderpublish" action="getBannerdetails" params="'siteId='+siteId" onSuccess="showBanners(data);"/>
    }

    getBannerDetails(siteId);

    function replaceAll(str, find, replace) {
        if (str == undefined) return str
        else return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }

    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }


    document.addEventListener("DOMContentLoaded", function() {
        getLatestBooks();
    });

    function redirectToProductPage(title,id,publisher) {
        var redirectionURL = "/" + replaceAll(title,' ','-') +"/ebook-details?siteName="+"${session['siteName']}"+"&bookId=" + id +"&publisher="+replaceAll(publisher,' ','-')+"&preview=true"
        window.location.href = redirectionURL;
        setTimeout(function (){
            window.open(redirectionURL,'_blank');
        },100)
    }
    function getLatestBooks(){
        let fromApp=false;
        let categories=true;
        let level=null;
        let syllabus=null;
        let grade=null;
        let subject=null;
        let pageNo=0;
        let publisherId=null;
        let subscriptionBooks=false;

        <g:remoteFunction controller="wonderpublish" action="getNewBooksList"  onSuccess='latestBooksReceived(data);'
                  params="'fromApp=false&categories=true&level='+level+'&syllabus='+syllabus+'&grade='+grade+'&subject='+subject+'&pageNo='+pageNo+'&publisherId='+publisherId+'&getSubscription='+subscriptionBooks" />
    }
    function latestBooksReceived(data){
        const container = document.querySelector(".scrollable-container");
        const content = document.querySelector(".scrollable-content");
        const scrollLeftBtn = document.querySelector(".scroll-button.left");
        const scrollRightBtn = document.querySelector(".scroll-button.right");
        var newlyReleasedBookList;
        let btnText;
        scrollLeftBtn.addEventListener("click", function() {
            content.scrollBy({
                left: -200, // Adjust the scroll amount as needed
                behavior: "smooth"
            });
        });

        scrollRightBtn.addEventListener("click", function() {
            content.scrollBy({
                left: 200, // Adjust the scroll amount as needed
                behavior: "smooth"
            });
        });
        <% if(session['wileySite'] == true){%>
        newlyReleasedBookList= '${newlyReleasedEbook}';
        btnText = 'Details';
        <%} else{%>
        newlyReleasedBookList = JSON.parse(data.books);
        btnText = 'Open';
        if (newlyReleasedBookList.length === 0){
            newlyReleasedBookList = 'Nothing present';
        }
        <%}%>
        var scrollContentHTML = "";
        var imgSrc="";

        if (newlyReleasedBookList!=null && newlyReleasedBookList!='Nothing present'){
            <% if(session['wileySite'] == true){%>
                newlyReleasedBookList = JSON.parse("${newlyReleasedEbook}".replace(/&quot;/g, '"').replaceAll('&#92;u0026', '&'));
            <%}%>
            newlyReleasedBookList.length >10 ? newlyReleasedBookList.splice(10) : newlyReleasedBookList;
            <% if("71".equals(""+session["siteId"])){%>
            //add an element at the top of newlyReleasedBookList
            newlyReleasedBookList.unshift({
                id: 91722,
                title: "SSC,Railway,Bank & Various Govt Exam - महापॅकेज",
                coverImage: "https://d1xcofdbxwssh7.cloudfront.net/live/supload/books/91722/processed/MPP.webp",
                publisher: "Kiran Institute of Career Excellence",
                listPrice: 0,
                offerPrice: 0,
                testsListprice: 0,
                testsPrice: 0,
                bookgptListPrice: 399,
                bookgptSellPrice: 29
            })
            <%}%>
            newlyReleasedBookList.map(book=>{
                imgSrc = book.coverImage;
                var coverImgPresent = true
                if (book.coverImage!=null && book.coverImage!='null' && book.coverImage.startsWith("https")) {
                    imgSrc = book.coverImage;
                    imgSrc = imgSrc.replace("~", ":");
                } else if(book.coverImage!=null && book.coverImage!='null' && !book.coverImage.startsWith("https")){
                    imgSrc = "/funlearn/showProfileImage?id=" + book.id + "&fileName=" + book.coverImage + "&type=books&imgType=webp";
                }else {
                    coverImgPresent = false
                }

                var bookOfferPriceVar=0
                var bookListPriceVar=0
                if (book.listPrice && book.offerPrice){
                    var offPercentage = (book.listPrice - book.offerPrice)
                    offPercentage  = Math.round(offPercentage/book.listPrice * 100)
                    bookOfferPriceVar = book.offerPrice
                    bookListPriceVar = book.listPrice
                }else if(book.testsPrice && book.testsListprice){
                    var offPercentage = (book.testsListprice - book.testsPrice)
                    offPercentage  = Math.round(offPercentage/book.testsListprice * 100)
                    bookOfferPriceVar = book.testsPrice
                    bookListPriceVar = book.testsListprice
                }else if(book.bookgptSellPrice && book.bookgptListPrice){
                    var offPercentage = (book.bookgptListPrice - book.bookgptSellPrice)
                    offPercentage  = Math.round(offPercentage/book.bookgptListPrice * 100)
                    bookOfferPriceVar = book.bookgptSellPrice
                    bookListPriceVar = book.bookgptListPrice
                }


                scrollContentHTML+="<div class='new-arrival' onclick=\"redirectToProductPage('"+book.title+"','"+book.id+"','"+book.publisher+"')\">";
                    if(coverImgPresent){
                        scrollContentHTML+= "<img src='"+imgSrc+"' alt='"+book.title+"' class='product-image'>";
                    }else{
                        scrollContentHTML+="<div class='unknownCoverImg'>"+
                        "<div class='unknownCoverImgTitle'>"+
                            "<p>"+book.title+"</p>"+
                        "</div>"+
                    "</div>";
                    }
                scrollContentHTML+="<div class='eBookText'>" ;
                <% if(session['wileySite'] == true){%>
                scrollContentHTML+="<p style='font-size: 11px'>EBOOK</p>";
                    <%}%>
                scrollContentHTML+="</div>"+
                    "<h3 class='product-name'>"+book.title+"</h3>"+
                    "<p class='publisher-name'>"+book.publisher+"</p>";
                <% if(!session['wileySite']){%>
                scrollContentHTML+="<div class='price-sec'>" +
                        "<span class='offerPriceText'>&#x20b9 "+bookOfferPriceVar+"</span>"+
                        "<span class='listPriceText'>&#x20b9 "+bookListPriceVar+"</span>";
                        if(bookOfferPriceVar > 0 && bookListPriceVar > 0){
                            scrollContentHTML+="<span class='offerPercentage'>("+offPercentage+"% off)</span>";
                        }
                scrollContentHTML+="</div>";
                if(book.bookType === "bookgpt" || book.bookType ==="ebookwithai"){
                    scrollContentHTML+="<a href='/prompt/bookgpt?siteName="+"${session['siteName']}"+"&bookId="+book.id+"' style='padding: 5px 10px;background: #F79420;color: #fff;border-radius: 5px;text-align: center;margin-top:auto;'>Try for Free</a>"
                }else if(book.printOnly == false){
                    scrollContentHTML+="<a href='/" + replaceAll(book.title,' ','-') + "/ebook?siteName="+"${session['siteName']}"+"&bookId=" + book.id+"&preview=true' style='padding: 5px 10px;background: #F79420;color: #fff;border-radius: 5px;text-align: center;margin-top:auto;'>Try for Free</a>"
                }
                <%}%>
                <% if(session['wileySite'] == true){%>
                scrollContentHTML+="<button class='view-button' onclick=\"redirectToProductPage('"+book.title+"')\">"+btnText+"</button>";
                    <%}%>
                scrollContentHTML+="</div>";
            })

            let cardCount  = 5;
            <% if(session['wileySite'] == true){%>
            cardCount  = 3;
            <%}%>
            if (window.innerWidth >= 768) {
                if (newlyReleasedBookList.length > cardCount) {
                    document.querySelector('.scroll-buttons').classList.remove('d-none');
                }
            }else{
                if (newlyReleasedBookList.length >= 2) {
                    document.querySelector('.scroll-buttons').classList.remove('d-none');
                }
            }
            document.getElementById('newReleaseBooks').innerHTML = scrollContentHTML;
            document.querySelector('.newArrivals').classList.remove('d-none');
        }
        const unknownCvr = document.querySelectorAll('.unknownCoverImg')
        var colors=['#2EBAC6','#0D5FCE','#6FCF97','#F2C94C','#C20232','#FC7753','#E40039','#1abc9c','#FD7272','#55E6C1','#17c0eb'];
       if (unknownCvr.length>0){
           unknownCvr.forEach(cov=>{
               const randomColorNumber = Math.floor(Math.random() * 11);
                cov.style.background=colors[randomColorNumber]
           })
       }
    }
</script>

</body>
</html>
