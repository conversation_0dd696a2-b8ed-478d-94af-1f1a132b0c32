<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<style>
.datepicker table {
    border-collapse: unset;
}
.datepicker .datepicker-days td, .datepicker .datepicker-days th {
    width: 25px !important;
    height: 25px !important;
}
.disabledEditBadge{
    padding: 11px;
    font-size: 12px;
    background: red;
    color: #fff;
    cursor: not-allowed;
}
</style>

<script>
    <%if(booksTagDtl!=null){%>
    noOfTags = ${booksTagDtl.size()};
    <%}%>
</script>
<div class="book_details_info">
    <div class="d-flex align-items-center flex-wrap mb-3">
        <h5 class="create-book-title mb-2 mb-md-0"><strong>Create or Edit eBook</strong></h5>
        <%if(params.bookId!=null){%>
        <a href="javascript:copyBook('${params.bookId}','normal')" class="btn btn-primary ml-0 ml-md-3">Copy eBook</a>
        <sec:ifAnyGranted roles="ROLE_PDF_EXTRACTOR,ROLE_GPT_MANAGER">
            <a href="/resourceCreator/aiBookMigrate?bookId=${params.bookId}" class="btn btn-primary ml-0 ml-md-3" target="_blank">AI Book Creation</a>
        </sec:ifAnyGranted>
        <%if("true".equals(session["commonWhiteLabel"])){%>
        <a href="/library/first-book-for-testing?checkurl=true&siteName=${session['siteName']}&bookId=${params.bookId}" class="btn btn-primary ml-3" target="_blank">View eBook</a>
        <%}else{%>
        <a href="/library/first-book-for-testing?checkurl=true&siteName=${session['entryController']}&bookId=${params.bookId}" class="btn btn-primary ml-3" target="_blank">View eBook</a>
        <%}%>
        <%}%>

        <%-- Lock Edit functionality for site ID 71 and ROLE_PDF_EXTRACTOR --%>
        <% if("71".equals(""+session['siteId'])){ %>
        <% if(booksMst != null && "true".equals(booksMst.lockEdit)) { %>
        <span class="badge badge-warning ml-3 disabledEditBadge">Edit is Locked</span>
        <% } else { %>
        <sec:ifAnyGranted roles="ROLE_PDF_EXTRACTOR">
            <a href="javascript:showLockEditConfirmation()" class="btn btn-warning ml-3">Lock Edit</a>
        </sec:ifAnyGranted>
        <% } %>
        <%}%>
        <%if(booksDtl==null||booksDtl.masterBookId==null){%>
        <sec:ifAnyGranted roles="ROLE_GPT_MANAGER">
            <a href="/resources/childBookInput?bookId=${params.bookId}" class="btn btn-primary ml-0 ml-md-3" target="_blank">Create Child Book</a>
        </sec:ifAnyGranted>
        <%}%>
    </div>


    <div class="row">
        <div class="col-12 col-md-7">
            <div class="row align-items-center ebook-title">
                <div class="form-group col-8">
                    <label>Title</label>
                    <input name="title" id="title" class="form-control" type="text" placeholder="Enter eBook title here." size="255" onblur="javascript:bookDtlUpdate(this);"
                           value="<%= booksMst !=null?booksMst.title:"" %>">
                </div>
                <div class="col-4 px-0"><a href='javascript:getDeepLink(null,null,null,"book")' class='text-primary'>Deep Link</a></div>
                <div class="invalid-feedback col booktitlealert" id="" style="display: none;">Enter the eBook title.</div>
            </div>


            <%if(params.bookId!=null){%>
            <div class="row align-items-center">
                <div class="form-group col-8">
                    <label>Book ID</label>
                    <input name="bookIdDisplay" id="bookIdDisplay" class="form-control" type="text" placeholder="Enter book id here." size="255"  value="${params.bookId}" readonly>
                    </div>
            </div>
            <%}%>
            <%if(booksDtl!=null&&booksDtl.masterBookId!=null){%>
            <div class="row align-items-center">
                <div class="form-group col-8">
                    <label>Master Book ID</label>
                    <input name="masterBookId" id="masterBookId" class="form-control" type="text" placeholder="Enter master book id here." size="255"  value="${booksDtl.masterBookId}" readonly>
                    </div>
            </div>
            <%}%>

            <div class="row align-items-center publisher-select">
                <div class="form-group col-8">
                    <label>Publisher</label>
                    <g:select id="publisherId" class="form-control" optionKey="id" optionValue="name" onchange="javascript:bookDtlUpdate(this);"
                              value="${booksMst?booksMst.publisherId:""}" name="publisherId" from="${publishers}" noSelection="['':'Select']"/>
                </div>
                <% if(session["userdetails"].publisherId==null){%>
                <div class="col-4 px-0"><a href="javascript:addNewPublisher()">Add New Publisher</a></div>
                <%}%>
                <div class="invalid-feedback col" id="publisher-error" style="display: none;">Please select publisher.</div>
            </div>
            <div class="row align-items-center author-select">
                <div class="form-group col-8">
                    <label>Author</label>
                    <g:select  class="selectpicker" id="authors" name="authors" from="${authors}" optionKey="id" optionValue="name" multiple="multiple" title="Select Author/s" data-live-search="true"/>
                </div>
                <div class="col-4 px-0"> <a href="javascript:addNewAuthor();" id="newAuthorLabel">Add New Author</a></div>
                <div class="invalid-feedback col" id="authors-error" style="display: none;">Please select author(s).</div>
            </div>

            <div class="row align-items-center">
                <div class="form-group col-8">
                    <label>Book Type</label>
                    <select name="bookType" class="form-control" id="bookTypeId" onchange="javascript:bookDtlUpdate(this);"><option>Select one</option>
                        <option value="ebook" <%= booksMst !=null&&"ebook".equals(booksMst.bookType)?"selected":""%>>eBook</option>
                        <option value="previouspapers" <%= booksMst !=null&&"magazine".equals(booksMst.bookType)?"selected":""%>>Magazine</option>
                        <option value="mcqsebook" <%= booksMst !=null&&"mcqsebook".equals(booksMst.bookType)?"selected":""%>>MCQs eBook</option>
                        <option value="liveclasses" <%= booksMst !=null&&"liveclasses".equals(booksMst.bookType)?"selected":""%>>Live Classes</option>
                        <option value="onlinecourse" <%= booksMst !=null&&"onlinecourse".equals(booksMst.bookType)?"selected":""%>>Online Course</option>
                        <option value="testseries" <%= booksMst !=null&&"testseries".equals(booksMst.bookType)?"selected":""%>>Test Series</option>
                        <option value="previouspapers" <%= booksMst !=null&&"previouspapers".equals(booksMst.bookType)?"selected":""%>>Previous Papers</option>
                        <option value="ebookwithai" <%= booksMst !=null&&"ebookwithai".equals(booksMst.bookType)?"selected":""%>>eBook With AI</option>
<sec:ifAllGranted roles="ROLE_GPT_MANAGER">
                        <option value="bookgpt" <%= booksMst !=null&&"bookgpt".equals(booksMst.bookType)?"selected":""%>>Book GPT</option>
                        <option value="recharge" <%= booksMst !=null&&"recharge".equals(booksMst.bookType)?"selected":""%>>Recharge Voucher</option>
</sec:ifAllGranted>
                    </select>
                    <div class="invalid-feedback" id="booktype-error" style="display: none;">Please select book type.</div>
                </div>
                <div class="col-4 px-0">
                    <label for="bookExpiry">Book Expiry:</label>
                    <input type="text" class="form-control" name="bookExpiry" id="bookExpiry" onchange="javascript:bookDtlUpdate(this);" value="<%= booksMst !=null&&booksMst.bookExpiry!=null?(""+booksMst.bookExpiry).substring(0,10):"" %>">
                </div>
            </div>

            <% if("12".equals(""+session.getAttribute("siteId"))||"23".equals(""+session.getAttribute("siteId"))||"24".equals(""+session.getAttribute("siteId"))) { %>
            <div class="row align-items-center">
                <div class="form-group col-8">
                    <label for="vendor">Vendor</label>
                    <select name="vendor" class="form-control" id="vendor" onchange="javascript:bookDtlUpdate(this);"><option>Select one</option>
                        <option value="zaza" <%= booksMst !=null&&"zaza".equals(booksMst.vendor)?"selected":""%>>ZaZa</option>
                        <option value="fidus" <%= booksMst !=null&&"fidus".equals(booksMst.vendor)?"selected":""%>>Fidus</option>
                        <option value="digiultra" <%= booksMst !=null&&"digiultra".equals(booksMst.vendor)?"selected":""%>>Digiultra</option>
                    </select>
                </div>
            </div>
            <% } %>
            <% if("9".equals(""+session.getAttribute("siteId"))) { %>
            <div class="row align-items-center">
                <div class="form-group col-8">
                    <label for="tags">Subject</label>
                    <select name="tags" id="tags" class="form-control subject-tags-select" onchange="javascript:bookDtlUpdate(this);" multiple style="min-height: 100px;">
                        <option value=""></option>
                        <% tags.each{tag-> %>
                        <option value="${tag.name}" <%=booksMst!=null && booksMst.tags!=null && booksMst.tags.indexOf(tag.name)>-1?"selected":""%>>${tag.name}</option>
                        <% } %>
                    </select>
                </div>
            </div>
            <% } %>

        </div>

        <div class="col-12 col-md-5">
            <div class="card coverimageWrapper">
                <h4 class="mt-2">Book Images</h4>
                <div class="form-group uploadimages d-flex align-items-center justify-content-around">

                    <div>
                        <form class="form-horizontal" enctype="multipart/form-data" role="form" name="uploadbookcover" id="uploadbookcover" action="/wonderpublish/uploadBookCover" method="post">
                            <input type="hidden" name="bookId">
                            <input type="hidden" name="imgType" value="cover">

                            <div class="row justify-center text-left mx-0">
                                <div class="col-md-12 text-left image-wrapper overlay-fade-in" id="bookcover">
                                    <%if(booksMst!=null&&booksMst.coverImage!=null){
                                    if(booksMst.coverImage.startsWith("http")){%>
                                    <img src="${booksMst.coverImage}" width="100">
                                    <%}else{%>
                                    <img src="/funlearn/showProfileImage?id=${booksMst.id}&fileName=${booksMst.coverImage}&type=books&imgType=webp" width="100">
                                    <%}}else{%> <a class="icon" href="javascript:void(0);" ><i class="material-icons">description</i><span>Book Cover Image</span></a><%}%>
                                    <div class="image-overlay-content image-upload">
                                        <p><br></p>
                                        <div id="filelabel1"><label for="fileoption1"><span class="smallText" style="cursor: pointer;color:white;">Upload Cover Image</span></label></div>
                                        <input id="fileoption1" name="file" type="file"  accept="image/png, image/jpeg, image/gif, image/webp" onchange="updateBookCover('cover',this);"/></div>
                                </div>

                            </div>
                            <div class="upload-book-iame-helper" style="margin-top: 12px; font-size:14px;text-align: center;">180px by 240px</div>

                        </form>

                    </div>
                    <%if(!institutePublisher){%>
                    <div>
                        <form class="form-horizontal" enctype="multipart/form-data" role="form" name="uploadbookheader" id="uploadbookheader" action="/wonderpublish/uploadBookCover" method="post">
                            <input type="hidden" name="bookId">
                            <input type="hidden" name="imgType" value="header">
                            <input type="hidden" name="smartEbook" value="${smartEbook}">

                            <div class="row justify-center text-left mx-0"><div class="col-md-12 text-left image-wrapper overlay-fade-in" id="bookcover">
                                <%if(booksMst!=null && booksMst.headerImage!=null){%>
                                <img src="/funlearn/showProfileImage?id=${booksMst.id}&fileName=${booksMst.headerImage}&type=books&imgType=webp" width="100">
                                <%}else{%> <a class="icon1" href="#" ><i class="material-icons">description</i></i><span>Book Header Image</span></a><%}%>
                                <div class="image-overlay-content image-upload">
                                    <p><br></p>
                                    <div id="filelabel2"><label for="fileoption2"><span class="smallText" style="cursor: pointer;color:white;text-align: center;">Upload Header Image</span></label></div>
                                    <input id="fileoption2" name="file" type="file"  accept="image/png, image/jpeg, image/gif" onchange="updateBookCover('header',this);"/></div>

                            </div>

                            </div>
                            <div class="upload-book-iame-helper" style="margin-top: 12px;font-size:14px; text-align: center;">1280px by 240px</div>

                        </form>
                    </div>
                    <%}%>
                </div>
            </div>
        </div>
    </div>

    <%if(showDownloadChapters){%>
    <%
            boolean downloadChapters=true
            if(booksMst==null) {
                downloadChapters = false
            }
            if(booksMst !=null){
                if (booksMst.downloadChapters == null || ("No".equals(booksMst.downloadChapters))){
                    downloadChapters = false
                }
            }
    %>
    <div class="row align-items-start mt-3 mt-md-0">
        <div class="form-group col-6 col-md-3">
            <label>Download Chapters</label>
            <label class="clickable-label">
                <input type="radio" name="downloadChapters" value="No" <%=!downloadChapters?"checked":"" %> onblur="javascript:bookDtlUpdate(this);" class="radioDownload"> No
            </label>
            <label class="clickable-label">
                <input type="radio" name="downloadChapters" id="downloadChapters" value="Yes" <%=downloadChapters?"checked":"" %> onblur="javascript:bookDtlUpdate(this);"  class="radioDownload"> Yes
            </label>
        </div>
        <div class="form-group col-6 col-md-3" id="downloadChaptersNo">
            <label for="chapterDownloadCount">No. of chapters to download</label>
            <select name="chapterDownloadCount" class="form-control" id="chapterDownloadCount" onchange="javascript:bookDtlUpdate(this);"><option value="">Select one</option>
                <option value="1" <%= booksMst !=null&&booksMst.chapterDownloadCount==1?"selected":""%>>1</option>
                <option value="2" <%= booksMst !=null&&booksMst.chapterDownloadCount==2?"selected":""%>>2</option>
            </select>
            <div class="invalid-feedback" id="chapterDownloadCountError" style="display: none;">Please select no. of chapters to download.</div>
        </div>
    </div>
    <%}%>

    <%if(!institutePublisher){%>
    <div class="row align-items-start mt-3 mt-md-0">
        <div class="form-group col-6 col-md-3">
            <label>ISBN</label>
            <%  if("evidya".equals(session["entryController"])|| "etexts".equals(session["entryController"])|| "ebouquet".equals(session["entryController"])) { %>
            <input type="text" class="form-control" name='isbn' id="isbn" maxlength="13" pattern="[0-9]*" value="<%= booksMst !=null?booksMst.isbn:"" %>" placeholder="ISBN" onblur="javascript:bookDtlUpdate(this);">
            <%} else {%>
            <input type="text" class="form-control" name='isbn' id="isbn" pattern="[0-9]*" value="<%= booksMst !=null?booksMst.isbn:"" %>" placeholder="ISBN" onblur="javascript:bookDtlUpdate(this);">
            <%}%>
            <div id="isbnerror" class="invalid-feedback"></div>
        </div>
   <div class="form-group col-6 col-md-3">
            <label for="bookWeight">Book Weight</label>
            <input type="number" class="form-control" name='bookWeight' id="bookWeight" min="0" pattern="^\d+\.{0,1}\d{0,2}$" value="<%= booksMst !=null?booksMst.bookWeight:"" %>" placeholder="Weight (in grams)"  onblur="javascript:bookDtlUpdate(this);" autocomplete="off">
   </div>

        <div class="form-group col-6 col-md-3">
            <label for="title">ASIN</label>
            <input type="number" class="form-control" name='asin' id="asin" value="<%= booksMst !=null?booksMst.asin:"" %>" placeholder="ASIN" onblur="javascript:bookDtlUpdate(this);" autocomplete="off">
        </div>
    <div class="form-group col-6 col-md-3">
        <label for="title">Current Stock</label>
        <input type="number" class="form-control" name='currentStock' id="currentStock" value="<%= booksMst !=null?booksMst.currentStock:"" %>" placeholder="Current Stock" onblur="javascript:bookDtlUpdate(this);" autocomplete="off">
    </div>
    </div>

    <% if("12".equals(""+session.getAttribute("siteId"))|| "23".equals(""+session.getAttribute("siteId"))||"24".equals(""+session.getAttribute("siteId"))) { %>
    <div class="row align-items-center">
        <div class="form-group col-md-6">
            <label for="packageBookIds">keywords</label>
            <span class="small">** Enter comma "," seperated values, without spaces</span>
            <textarea rows="5" cols="50" id="isbnkeywordsIds" name="f" class="form-control" style="resize: unset;"><%= keywordStr !=null?keywordStr:"" %></textarea>
            <div class="invalid-feedback" id="keywords-error">Please enter ISBN to add the keywords.</div>
            <div class="valid-feedback" id="keywords-success">Keywords updated successfully!</div>
            <button class="btn btn-default mt-2 updateBtn" onclick="updateIsbnKeyword()">Update</button>
        </div>
    </div>
    <% } %>

    <div class="row align-items-center">
        <div class="form-group col-md-5">
            <label>Package eBook Id/Id's</label>
            <span class="small">** Comma separated eBook ids for multiple eBooks</span>
            <textarea  class="form-control" rows="2" cols="20" id="packageBookIds" name="packageBookIds"><%= booksMst !=null?booksMst.packageBookIds:"" %></textarea>
            <button class="btn btn-default mt-2 updateBtn" onclick="javascript:updatePackageBooks();">Update</button>
        </div>
        <%boolean showInLibrary=true
            if(booksMst !=null&&"No".equals(booksMst.showInLibrary)) showInLibrary = false
        %>
        <div class="col-6 col-md-2">
            <div class="form-group">
                <label for="showInLibrary">Show in library</label>
                <label class="clickable-label">
                    <input type="radio" name="showInLibrary" id="showInLibrary" value="Yes" <%=showInLibrary?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">Yes
                </label>
                <label class="clickable-label">
                    <input type="radio" name="showInLibrary" value="No" <%=!showInLibrary?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">No
                </label>
            </div>
        </div>

        <%boolean allowSubscription=false
            if(booksMst !=null&&"Yes".equals(booksMst.allowSubscription)) allowSubscription = true
        %>
        <div class="col-6 col-md-2">
            <div class="form-group">
                <label for="allowSubscription">Allow Subscription</label>
                <label class="clickable-label">
                    <input type="radio" name="allowSubscription" id="allowSubscription" value="Yes" <%=allowSubscription?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">Yes
                </label>
                <label class="clickable-label">
                    <input type="radio" name="allowSubscription" value="No" <%=!allowSubscription?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">No
                </label>
            </div>
        </div>
        <%boolean subscriptionPackage=false
            if(booksMst !=null&&"Yes".equals(booksMst.subscriptionPackage)) subscriptionPackage = true
        %>

        <div class="col-6 col-md-2">
            <div class="form-group">
                <label for="allowSubscription">Subscription Package</label>
                <label class="clickable-label">
                    <input type="radio" name="subscriptionPackage" id="subscriptionPackage" value="Yes" <%=subscriptionPackage?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">Yes
                </label>
                <label class="clickable-label">
                    <input type="radio" name="subscriptionPackage" value="No" <%=!subscriptionPackage?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">No
                </label>
            </div>
        </div>

        <div class="form-group col-md-3">
            <label>Transfer To:</label>
            <select class="form-control" name="transferto" id="transferto" >
                <option value="">Select</option>
                <% if(publisherAccessUsers!=null){
                    for(int i=0;i<publisherAccessUsers.size();i++){
                %>
                <option value="${publisherAccessUsers[i].username}">${publisherAccessUsers[i].username}</option>
                <%}}%>

            </select>
            <button class="btn btn-default mt-2 updateBtn"  onclick="updateCreatedBy()">Update</button> </i>
        </div>
    <div class="form-group col-md-3">
        <label>Subscription</label>
        <select class="form-control" name="subscriptionId" id="subscriptionId" onchange="javascript:bookDtlUpdate(this);">
            <option value="">Select</option>
            <% if(subscriptions!=null){
                for(int i=0;i<subscriptions.size();i++){
            %>
            <option value="${subscriptions[i].id}" <%=booksMst!=null && booksMst.subscriptionId!=null && (""+subscriptions[i].id).equals(booksMst.subscriptionId)?"selected":""%>>${subscriptions[i].title}</option>
            <%}}%>

        </select>

    </div>
    <%boolean genericReader=false
        if(booksMst !=null&&"Yes".equals(booksMst.genericReader)) genericReader = true
    %>

        <div class="form-group col-md-3">
            <label for="showInLibrary">Generic Reader</label>
            <label class="clickable-label">
                <input type="radio" name="genericReader" id="genericReader" value="Yes" <%=genericReader?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">Yes
            </label>
            <label class="clickable-label">
                <input type="radio" name="genericReader" value="No" <%=!genericReader?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">No
            </label>
        </div>




    </div>


    <%if(smartEbook){%>
    <div class="row align-items-center">
        <div class="form-group col-md-4">
            <label>Preview Chapter</label>
            <select class="form-control" name="previewChapter" id="previewChapter" onchange="javascript:previewChapterUpdate(this)">
                <option value="">Select chapter</option>
                <% if(chaptersMst!=null){
                    for(int i=0;i<chaptersMst.size();i++){
                        if("toc".equals(chaptersMst[i].chapterDesc)) continue;%>
                <option value="${chaptersMst[i].id}" <%="true".equals(chaptersMst[i].previewChapter)?"selected":""%>>${chaptersMst[i].name}</option>
                <%}
                }%>
            </select>

        </div>
        <div class="form-group col-md-4">
            <label>Book Validity (in days)</label>
            <input type="number" class="form-control" name="validityDays" id="validityDays" size="5" oninput="this.value =
                !!this.value && Math.abs(this.value) >= 0 ? Math.abs(this.value) : null" value="<%= booksMst !=null?booksMst.validityDays:"" %>" onblur="javascript:bookDtlUpdate(this);" onkeypress="return onlyNumberKey(event)">

        </div>

        <div class="form-group col-md-4">
            <label>BOOK CODE</label>
            <input type="text" class="form-control" name="bookCode" id="bookCode" value="<%= booksMst !=null?booksMst.bookCode:"" %>" onblur="javascript:bookDtlUpdate(this);" placeholder="Book Code">
        </div>

        <g:uploadForm name="resource2Form" url="[action:'addFile',controller:'resourceCreator']"  method="post">
            <input type="hidden" name="resourceType" value="Notes">
            <input type="hidden" name="useType" value="notes">
            <input type="hidden" name="chapterId">
            <input type="hidden" name="bookId">
            <input type="hidden" name="quizMode" value="toc">

            <!--  <div class="image-upload">
                  <div id="tocchapter"><%if(resourceDtl!=null){%>
            <a href="javascript:showBook('${resourceDtl.id}','${bookId}','${resourceDtl.chapterId}','${resourceDtl.noOfPages}')" class='darkgrey'>
                                    <span class='light10text'>${resourceDtl.resourceName}</span> </a>&nbsp;&nbsp;<label for='file2'><span style='cursor: pointer' class="logoblue">&nbsp; Change table of contents <i class="fa fa-upload fa-x"></i></span>&nbsp;( epub only) </label><%}
        else{%><label for='file2'><span style='cursor: pointer' class="logoblue">&nbsp; Upload table of contents <i class="fa fa-upload fa-x"></i></span>&nbsp;( epub only) </label><%}%></div>
                                <input id='file2' name='file' type='file'  accept=".epub" onchange='updatetoc();'/>
                            </div>-->
        </g:uploadForm>
    </div>
    <%}%>
    <small class="smallerText greyText"><i class="bukerror">** Please select preview chapter, after adding the chapters.</i></small>
    <div class="row align-items-center mt-2">
        <div class="form-group col-md-4">
            <label for="description">Language</label>
            <g:select id="language" class="form-control" name="language" onchange="javascript:bookDtlUpdate(this);"
                      from="${langMstList}"
                      value="${booksMst?booksMst.language:""}"
                      optionKey="language" optionValue="language" noSelection="['':'Select']" />
        </div>
        <div class="form-group col-md-4">
            <label>Source</label>
            <input type="text" class="form-control" name="source" id="source" size="5" value="<%= booksMst !=null?booksMst.source:"" %>" onblur="javascript:bookDtlUpdate(this);" placeholder="Url - source for free book">

        </div>
        <div class="form-group col-md-4">
            <label for="description">Medium</label>
            <g:select id="language" class="form-control" name="medium" onchange="javascript:bookDtlUpdate(this);"
                      from="${langMstList}"
                      value="${booksMst?booksMst.medium:""}"
                      optionKey="language" optionValue="language" noSelection="['':'Select']" />
        </div>
    </div>
    <%}%>
    <div class="row align-items-center">
        <div class="form-group col-12 addCategories">
            <label>Add Category Tags</label>
            <span>** This will help in searching and categorizing eBooks.</span>
            <span>** Add relevant category of book.</span>
            <span>** you can add more than one category</span>
            <div class="row mt-2">
                <div class="form-group col-6 col-md-3">
                    <g:select id="createLevel" class="form-control" name="createLevel" from="${levelsMstList}" optionKey="name" optionValue="name"
                              noSelection="['':'Select']" onchange="javascript:getCreateSyllabus(this.value)"/>
                    <div class="invalid-feedback" id="categoryTagsError">Please select category tags.</div>
                </div>
                <div class="form-group col-6 col-md-3">
                    <select id="createSyllabus" class="form-control" name="createSyllabus"  onchange="javascript:getCreateGrade(this.value)" style="display: none"><option>Select</option></select>
                </div>
                <div class="form-group col-6 col-md-3">
                    <select id="createGrade" class="form-control" name="createGrade"  onchange="javascript:getCreateSubject(this.value)" style="display: none"><option>Select</option></select>
                </div>
                <div class="form-group col-6 col-md-3">
                    <select id="createSubject" class="form-control" name="createSubject"  onchange="javascript:addTag(this.value)" style="display: none"><option>Select</option></select>
                </div>
            </div>
        </div>

    </div>


    <div class="row mx-0 mb-3" style="overflow-x: scroll;">
        <div class="col-12 px-0 invalid-feedback" id="deleteTagError">Cannot delete the last tag of the published book.</div>
        <table class="table table-bordered col-12 col-md-10" id="addedtags">
            <%if(booksTagDtl!=null){
                for(int i=0; i<booksTagDtl.size();i++){%>
            <tr>
                <td>&nbsp;${booksTagDtl[i].level}</td>
                <td>&nbsp;${booksTagDtl[i].syllabus}</td>
                <td>&nbsp;${booksTagDtl[i].grade}</td>
                <td>&nbsp;${booksTagDtl[i].subject}</td>
                <td width='5%'><a href="javascript:deleteBookTag(${booksTagDtl[i].id})"><i class="material-icons delete">delete_outline</i> </a></td>
            <%if(gptManager){%>
            <td width='5%'><a href="/contentCreation/getSolvedPaperList?bookId=${bookId}&bookTagId=${booksTagDtl[i].id}">Create Question Paper Chapters </a></td>
            <%}%>
        </tr>
            <%}}%>
        </table>
    </div>


    <%if(!institutePublisher){%>
    <div class="row align-items-center">
        <div class="form-group col-md-8">
            <label for="description">Book Description</label>
            <textarea class="form-control" rows="3" name='description' id="description"  placeholder="Book Description"  maxlength="2000"></textarea>
        </div>
    </div>
    <%if(gptManager){%>
    <div class="row align-items-center">
        <div class="form-group col-md-8">
            <label for="description">Book level prompt</label>
            <textarea class="form-control" rows="3" name='basePrompt' id="basePrompt"  placeholder="Book Description"  maxlength="500" onchange="javascript:bookDtlUpdate(this);"><%= booksMst !=null?booksMst.basePrompt:"" %></textarea>
        </div>
    </div>
    <%}%>

    <div class="row align-items-center">
        <div class="col-md-3">
            <div class="form-group">
                <label for="showInLibrary">Upload EPUB file</label>
                <label class="clickable-label">
                    <input type="radio" name="showEpubUpload" id="showEpubUpload" value="Yes" onchange="javascript:showEpubUpload(this);">Yes
                </label>
                <label class="clickable-label">
                    <input type="radio" name="showEpubUpload" value="No" checked onchange="javascript:showEpubUpload(this);">No
                </label>
            </div>
        </div>
    </div>

    <div class="row align-items-center">
        <div class="col-md-3">
            <div class="form-group">
                <label for="showInLibrary">Upload PDF file</label>
                <label class="clickable-label">
                    <input type="radio" name="showPdfUpload" id="showPdfUpload" value="Yes" onchange="javascript:showPdfUpload(this);">Yes
                </label>
                <label class="clickable-label">
                    <input type="radio" name="showPdfUpload" value="No" checked onchange="javascript:showPdfUpload(this);">No
                </label>
            </div>
        </div>
    </div>

    <div id="displayEpubUpload" class="row align-items-start" style="display: none">
        <div class="col-md-4">
            <div class="form-group">
                <input type="file" id="epubFileInput" class="form-control">
                <div class="invalid-feedback" id="epubFileError">Please select epub file to upload.</div>
            </div>
        </div>
        <button type="button" id="epubUploadButton" onclick="epubUploadButtonClicked()" class="btn btn-default mb-3 ml-3 ml-md-0 updateBtn">Upload</button>
    </div>
<g:form url="[controller: 'excel', action: 'splitPdf']" method="POST" enctype="multipart/form-data" id="pdfUploadForm">
    <input type="hidden" name="bookId" value="${params.bookId}">
    <div id="displayPdfUpload" class="row align-items-start" style="display: none">
       <div class="col-md-4">
            <div class="form-group">
                TOC Page Nos:
                <input type="text" id="tocPageNos" name="tocPageNos" class="form-control"><br>
                1st Chapter Page Number:
                <input type="number" id="firstChapterPageNo" name="firstChapterPageNo" class="form-control"><br>

            </div>
        </div>
    <div class="col-md-4">
    <div class="form-group">
        <label for="pdfFile">Select PDF</label>
        <input type="file" id="pdfFile" name="pdfFile" class="form-control" required>
        <div class="invalid-feedback" id="pdfFileError">Please select pdf file to upload.</div>
    </div>
    </div>
    <div class="col-md-4">
    <div class="form-group">
        <label for="excelFile">Select Excel</label>
        <input type="file" id="excelFile" name="excelFile" class="form-control">
        <div class="invalid-feedback" id="excelFileError">Please select excel file to upload.</div>
    </div>
    </div>
        <button type="submit" id="pdfUploadButton" class="btn btn-default mb-3 ml-3 ml-md-0 updateBtn">Upload</button>

        <!-- Loader with Timer -->
        <div id="pdfUploadLoader" class="col-12 mt-3" style="display: none;">
            <div class="d-flex align-items-center">
                <div class="spinner-border text-primary mr-3" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <div>
                    <div class="text-primary font-weight-bold">Processing PDF...</div>
                    <div class="text-muted">Time elapsed: <span id="uploadTimer">00:00</span></div>
                </div>
            </div>
        </div>
    </div>
</g:form>
    <%}%>
    <button type="button" id="nxt-btn" class="btn btn-primary my-2 pubdesk_btn" style="min-width: 150px;">Save and Continue</button>
    <%if(params.bookId!=null){%>
    <button type="button" id="nxt-btn-1" class="btn btn-primary my-2 pubdesk_btn" onclick="manageBookPrices(${params.bookId})" style="min-width: 150px;">Manage Price</button>
    <%}%>
    <sec:ifAnyGranted roles="ROLE_WS_CONTENT_ADMIN,ROLE_BOOK_CREATOR">
        <a href="javascript:unPublishBook();" id="published" class="btn btn-primary my-2 align-items-center justify-content-center pubdesk_btn" style="display: none;min-width: 150px;"><i class="material-icons mr-2" style="font-size: 16px">shopping_cart</i>Un-Publish</a>
    </sec:ifAnyGranted>
    <sec:ifAnyGranted roles="ROLE_WS_CONTENT_CREATOR,ROLE_BOOK_CREATOR">
            <a href="javascript:publish();" id="notpublished" style="display: none;min-width: 150px;" class="btn btn-primary align-items-center justify-content-center pubdesk_btn"><i class="material-icons mr-2">shopping_cart</i>Publish</a>
    </sec:ifAnyGranted>
    <div class="row mx-0">
        <div class="col-md-12 red smallText mx-0 pt-3 alert alert-danger mt-4" style="display: none"  id="publisherrors"></div>
    </div>
    <div class="col-md-2 orange px-0" id="booksaving" style="display: none">
        Saving...<i class="fa fa-spinner fa-3x fa-spin"></i>
    </div>

</div>

<!-- Lock Edit Confirmation Modal -->
<div class="modal fade" id="lockEditConfirmationModal" tabindex="-1" role="dialog" aria-labelledby="lockEditConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="lockEditConfirmationModalLabel">Confirm Lock Edit</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to lock editing for this Book? This action will prevent further modifications to the book.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="confirmLockEdit()">Lock Edit</button>
            </div>
        </div>
    </div>
</div>

<asset:javascript src="jszip.js"/>
<script src="https://cdn.jsdelivr.net/npm/epubjs/dist/epub.min.js"></script>
<script>

    function showEpubUpload(field) {
        if(field.value == "Yes") $("#displayEpubUpload").show();
        else $("#displayEpubUpload").hide()
    }

    function showPdfUpload(field) {
        if(field.value == "Yes") $("#displayPdfUpload").show();
        else $("#displayPdfUpload").hide()
    }

    var obj =[];
    var epubFileObj;
    var book = ePub();
    var rendition;
    var epubFileInputElement = document.getElementById("epubFileInput");

    // epubFileInputElement.addEventListener('change', function (e) {
    //     var file = e.target.files[0];
    //     epubFileObj = file;
    // });

    function getEpubBookDetails(e){
        var bookData = e.target.result;
        book.open(bookData);
        book.loaded.navigation.then(function(toc){
            // rendition.annotations.highlight("epubcfi(/6/14[xchapter_001]!/4/2/4/8[c001s0004],/1:334,/1:340)",{},function (e) {
            // });

            toc.forEach(function(chapter) {
                var jsonChapterString  = JSON.stringify(chapter);
                jsonChapterString = replaceAll(jsonChapterString,"&","and");
                obj.push(JSON.parse(jsonChapterString));
            });

            var formData = new FormData();
            formData.append('file', epubFileObj);
            formData.append('bookId', bookId);
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                type:'POST',
                url: '/resources/uploadSplitEpub',
                data:formData,
                cache:false,
                contentType: false,
                processData: false,
                success:function(data){
                    chaptersAdded(data)
                },
                error: function(data){
                    console.log("error");
                    console.log(data);
                }
            });

        });
    }

    function chaptersAdded(data) {
        $('.loading-icon').addClass('hidden');
        if(window.location.href.includes("bookId")) window.location.href = window.location.href;
        else window.location.href = window.location.href + "&bookId="+bookId;
    }

    function epubUploadButtonClicked() {
        if(document.getElementById("epubFileInput").files.length === 0) {
            $('#epubFileError').show();
            $('#epubFileInput').removeClass('input-success').addClass('input-error');
        } else {
            epubFileObj = document.getElementById("epubFileInput").files[0];
            if (window.FileReader) {
                var reader = new FileReader();
                reader.onload = getEpubBookDetails;
                reader.readAsArrayBuffer(epubFileObj);
            }
        }

    }

    function copyBook(bookId,copyType){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="resourceCreator" action="copyBook" onSuccess='bookCopied(data);' params="'bookId='+bookId+'&copyType='+copyType"></g:remoteFunction>
    }

    function bookCopied(data){
        if(data.status=="Success"){
            $('.loading-icon').addClass('hidden');
            $('#copiedBookModal').modal('show');
            $('#editCopiedBook').attr('onclick','openCopiedBook('+data.bookId+')');
        }else{
            $('.loading-icon').addClass('hidden');
            alert(data.status);
        }
    }

    function openCopiedBook(bookId) {
        $('.loading-icon').removeClass('hidden');
        window.location.href="/book-create-new?bookId="+bookId;
        return;
    }

    // PDF Upload Timer functionality
    var uploadStartTime;
    var timerInterval;

    function formatTime(seconds) {
        var minutes = Math.floor(seconds / 60);
        var remainingSeconds = seconds % 60;
        return (minutes < 10 ? "0" : "") + minutes + ":" + (remainingSeconds < 10 ? "0" : "") + remainingSeconds;
    }

    function startUploadTimer() {
        uploadStartTime = Date.now();
        timerInterval = setInterval(function() {
            var elapsedSeconds = Math.floor((Date.now() - uploadStartTime) / 1000);
            document.getElementById("uploadTimer").textContent = formatTime(elapsedSeconds);
        }, 1000);
    }

    function stopUploadTimer() {
        if (timerInterval) {
            clearInterval(timerInterval);
            timerInterval = null;
        }
    }

    // Handle PDF form submission
    $(document).ready(function() {
        $("#pdfUploadForm").on("submit", function(e) {
            // Show loader and start timer
            $("#pdfUploadLoader").show();
            $("#pdfUploadButton").prop("disabled", true);
            startUploadTimer();

            // The form will submit normally, but we've shown the loader
            // Note: The loader will be hidden when the page reloads after form submission
        });
    });

    // Lock Edit functionality
    function showLockEditConfirmation() {
        $('#lockEditConfirmationModal').modal('show');
    }

    function confirmLockEdit() {
        $('#lockEditConfirmationModal').modal('hide');
        <g:remoteFunction controller="wonderpublish" action="lockEditUpdate" onSuccess='lockEditUpdated(data);' onFailure='lockEditFailed(data);' params="'bookId='+bookId+'&lockEdit=true'"/>
    }

    function lockEditUpdated(data) {
        if(data.status == "success") {
            // Replace the Lock Edit button with Edit is Locked badge
            var lockEditButton = document.querySelector('a[href="javascript:showLockEditConfirmation()"]');
            if(lockEditButton) {
                lockEditButton.outerHTML = '<span class="badge badge-warning ml-3">Edit is Locked</span>';
            }
            // Show success message (optional)
            alert('Edit has been successfully locked for this eBook.');
        } else {
            alert('Error: ' + (data.message || 'Failed to lock edit. Please try again.'));
        }
    }

    function lockEditFailed(data) {
        alert('Error: Failed to lock edit. Please try again.');
    }

</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script>
    $('#bookExpiry').datepicker({
        format: 'yyyy-mm-dd',
        startView: 1,
        todayBtn: "linked",
        autoclose: true,
        todayHighlight: true,
        orientation: "bottom auto",

    });
</script>
