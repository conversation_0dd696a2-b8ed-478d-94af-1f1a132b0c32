<!DOCTYPE html>
<g:render template="/wonderpublish/loginChecker"></g:render>

<g:render template="/${session['entryController']}/navheader_new"></g:render>

<style>
    .search-container {
        max-width: 800px;
        margin: 50px auto;
        padding: 30px;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .search-form {
        display: flex;
        gap: 15px;
        flex-direction: column;
        margin-bottom: 30px;
    }

    .form-group {
        flex: 1;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #333;
    }

    .form-control {
        width: 100%;
        padding: 10px 15px;
        border: 2px solid #e1e5e9;
        border-radius: 5px;
        font-size: 14px;
        transition: border-color 0.3s;
    }

    .form-control:focus {
        outline: none;
        border-color: #007bff;
    }

    .btn-search {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 12px 25px;
        border-radius: 5px;
        font-weight: 600;
        cursor: pointer;
        transition: transform 0.2s;
        width: 250px;
    }

    .btn-search:hover {
        transform: translateY(-2px);
    }

    .btn-search:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
    }

    .results-container {
        margin-top: 30px;
    }

    .results-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .results-table th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px;
        text-align: left;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 12px;
        letter-spacing: 0.5px;
    }

    .results-table td {
        padding: 15px;
        border-bottom: 1px solid #f0f0f0;
    }

    .results-table tr:hover {
        background-color: #f8f9fa;
    }

    .status-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-published {
        background-color: rgba(40, 167, 69, 0.15);
        color: #28a745;
    }

    .status-draft {
        background-color: rgba(255, 193, 7, 0.15);
        color: #ffc107;
    }

    .status-unpublished {
        background-color: rgba(220, 53, 69, 0.15);
        color: #dc3545;
    }

    .loading-spinner {
        display: none;
        text-align: center;
        padding: 20px;
    }

    .spinner {
        border: 3px solid #f3f3f3;
        border-top: 3px solid #667eea;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin: 0 auto 10px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 15px;
        border-radius: 5px;
        margin-top: 20px;
        display: none;
    }

    .no-results {
        text-align: center;
        padding: 40px;
        color: #666;
        font-style: italic;
    }

    .page-title {
        text-align: center;
        margin-bottom: 30px;
        color: #333;
        font-size: 28px;
        font-weight: 700;
    }
</style>

<div class="search-container">
    <h1 class="page-title">Find Copied Books</h1>

    <div class="search-form">
        <div class="form-group">
            <label for="parentBookId">Parent Book ID</label>
            <input type="number" id="parentBookId" class="form-control" placeholder="Enter Parent Book ID" min="1">
        </div>
        <button type="button" id="searchBtn" class="btn-search" onclick="searchCopiedBooks()">
            <span id="btnText">Search Copied Books</span>
            <span id="btnSpinner" style="display: none;">Searching...</span>
        </button>
    </div>

    <div class="loading-spinner" id="loadingSpinner">
        <div class="spinner"></div>
        <div>Searching for copied books...</div>
    </div>

    <div class="error-message" id="errorMessage"></div>

    <div class="results-container" id="resultsContainer" style="display: none;">
        <table class="results-table">
            <thead>
                <tr>
                    <th>Book ID</th>
                    <th>Title</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody id="resultsTableBody">
            </tbody>
        </table>
    </div>
</div>

<script>
function searchCopiedBooks() {
    const parentBookId = document.getElementById('parentBookId').value;
    const searchBtn = document.getElementById('searchBtn');
    const btnText = document.getElementById('btnText');
    const btnSpinner = document.getElementById('btnSpinner');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const errorMessage = document.getElementById('errorMessage');
    const resultsContainer = document.getElementById('resultsContainer');
    const resultsTableBody = document.getElementById('resultsTableBody');

    // Validation
    if (!parentBookId || parentBookId.trim() === '') {
        showError('Please enter a Parent Book ID');
        return;
    }

    // Reset UI
    hideError();
    resultsContainer.style.display = 'none';

    // Show loading state
    searchBtn.disabled = true;
    btnText.style.display = 'none';
    btnSpinner.style.display = 'inline';
    loadingSpinner.style.display = 'block';

    // Make API call
    fetch('/resourceCreator/searchCopiedBooks', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'parentBookId=' + encodeURIComponent(parentBookId)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.error) {
            showError(data.error);
            return;
        }

        if (data.books && data.books.length > 0) {
            displayResults(data.books);
        } else {
            displayNoResults();
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        showError('An error occurred while searching. Please try again.');
    });
}

function displayResults(books) {
    const resultsTableBody = document.getElementById('resultsTableBody');
    const resultsContainer = document.getElementById('resultsContainer');

    resultsTableBody.innerHTML = '';

    books.forEach(book => {
        const row = document.createElement('tr');
        row.innerHTML = '<td>' + book.id + '</td>' +
            '<td>' + escapeHtml(book.title) + '</td>' +
            '<td><span class="status-badge status-' + book.status.toLowerCase() + '">' + book.status + '</span></td>';
        resultsTableBody.appendChild(row);
    });

    resultsContainer.style.display = 'block';
}

function displayNoResults() {
    const resultsTableBody = document.getElementById('resultsTableBody');
    const resultsContainer = document.getElementById('resultsContainer');

    resultsTableBody.innerHTML = '<tr><td colspan="3" class="no-results">No copied books found for this Parent Book ID</td></tr>';
    resultsContainer.style.display = 'block';
}

function showError(message) {
    const errorMessage = document.getElementById('errorMessage');
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
}

function hideError() {
    const errorMessage = document.getElementById('errorMessage');
    errorMessage.style.display = 'none';
}

function hideLoading() {
    const searchBtn = document.getElementById('searchBtn');
    const btnText = document.getElementById('btnText');
    const btnSpinner = document.getElementById('btnSpinner');
    const loadingSpinner = document.getElementById('loadingSpinner');

    searchBtn.disabled = false;
    btnText.style.display = 'inline';
    btnSpinner.style.display = 'none';
    loadingSpinner.style.display = 'none';
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Allow Enter key to trigger search
document.getElementById('parentBookId').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        searchCopiedBooks();
    }
});
</script>

<g:render template="/${session['entryController']}/footer_new"></g:render>