package com.wonderslate

import com.wonderslate.cache.DataProviderService
import com.wonderslate.cache.SecondDataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.sqlutil.SafeSql
import com.wonderslate.usermanagement.User
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import com.wonderslate.data.ObjectiveMst
import grails.transaction.Transactional
import com.wonderslate.data.ChaptersMst
import groovy.json.JsonSlurper

class WpmainController {
    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    SecondDataProviderService secondDataProviderService
    def redisService

    def index() {
        session["siteId"] = new Integer(1);
        if (springSecurityService.currentUser != null && session.getAttribute("userdetails") == null) {
            session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
        }
    }

    def unmarkedQA() {
        String sql = " select count(*) from objective_mst where question is not null and answer is not null and marks is null"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        [numberOfQA: results[0][0]]
    }

    def updateUnMarkedQA() {
        //find all from ObjectiveMst where question and answer is not null and marks is null order by id desc and limit 100
        List questions = ObjectiveMst.findAllByQuestionIsNotNullAndAnswerIsNotNullAndMarksIsNull([sort: "id", order: "desc", max: 500])
        int numberUpdated = 0, numberFailed = 0;
        questions.each { question ->
            //logic to update marks
            try {
                def sentences = question.answer.split("\\.")
                if (sentences.length == 1) question.marks = new Double(1)
                else if (sentences.length == 2 || sentences.length == 3) question.marks = new Double(2)
                else if (sentences.length == 4 || sentences.length == 5) question.marks = new Double(3)
                else if (sentences.length == 6 || sentences.length == 7) question.marks = new Double(4)
                else question.marks = new Double(5)
                ObjectiveMst.executeUpdate("update ObjectiveMst set marks=" + question.marks + " where id=" + question.id)
                numberUpdated++
            }
            catch (Exception e) {
                numberFailed++
                println("Exception while updating marks for question id " + question.id)
            }
        }
        def json = [status: "success", updatedTime: new Date(), numberUpdated: numberUpdated, numberFailed: numberFailed]
        render json as JSON
    }

    @Transactional
    def bookai() {
        String bookId = params.bookId
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
        if (redisService.("chapterId_resId_" + bookId) == null) {
            secondDataProviderService.getAllChapterIdResId(new Long(params.bookId));
        }
        List chaptersList = new JsonSlurper().parseText(redisService.("chapterId_resId_" + bookId))
        String userAgent = request.getHeader("User-Agent");
        Boolean isMobile = userAgent =~ /Mobile|Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/;
        def previewMode = true, hasBookAccess = true
        def showAIWindow = false

        // Get user roles for the template
        def userRoles = []
        if (springSecurityService.isLoggedIn()) {
            def user = springSecurityService.currentUser
            if (user) {
                userRoles = user.authorities?.collect { it.authority }
            }
        }

        [chaptersList   : chaptersList, bookId: bookId, isMobile: isMobile, previewMode: previewMode, hasBookAccess: hasBookAccess,
         bookTitle      : booksMst.title, showAIWindow: showAIWindow, userRoles: userRoles, title: "Teacher AI Assistant for " + booksMst.title,
         gptloaderName  : null,
         gptloaderpath  : null,
         gptcustomloader: null]
    }

    @Transactional
    def aibook() {
        String bookId = params.bookId
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
        if (redisService.("chapterId_" + bookId) == null) {
            dataProviderService.getChaptersList(new Long(bookId))
        }
        List chaptersList = new JsonSlurper().parseText(redisService.("chapterId_" + bookId))
        [booksMst: booksMst, chaptersList: chaptersList]

    }

}
