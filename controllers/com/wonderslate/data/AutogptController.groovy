package com.wonderslate.data

import com.wonderslate.cache.DataProviderService
import com.wonderslate.learn.FlowSegment
import com.wonderslate.log.AutogptLog
import com.wonderslate.log.GptDefaultCreateLog
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.Publishers
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonBuilder
import groovy.json.JsonSlurper
import grails.converters.JSON
import com.wonderslate.publish.PromptTemplateMst
import com.wonderslate.publish.PromptTemplateDtl

import java.text.SimpleDateFormat

class AutogptController {
    DataProviderService dataProviderService
    def redisService
    PromptService promptService
    GptLogService gptLogService
    def springSecurityService
    ResourceCreatorService resourceCreatorService
    AutogptService autogptService

    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def index() {
        //list of prompts sort by parentPromptType,promptLabel
        List prompts = Prompts.findAll([sort: [promptLabel: 'asc']])
        if (redisService.("chapters_" + params.bookId) == null) {
            dataProviderService.getChaptersList(new Long(params.bookId));
        }
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        List chaptersList = new JsonSlurper().parseText(redisService.("chapters_" + params.bookId))
        String gptServerUrl = promptService.getGPTServerUrl(request) + "/retrieveDataAdmin"
        List promptTemplates = PromptTemplateMst.findAll([sort: [name: 'asc']])
        [prompts : prompts, chaptersList: chaptersList, title: "AutoGPT", booksMst: booksMst, gptServerUrl: gptServerUrl,
         username: springSecurityService.currentUser.username, promptTemplates: promptTemplates]
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def setUpChapter() {
        String status
        //get the ResourceDtl for the given chapterId, resType="Notes, resLink ending with .pdf and gptResourceType is null"
        ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResLinkLikeAndGptResourceTypeIsNull(new Integer(params.chapterId), "Notes", "%.pdf")
        List<GptDefaultCreateLog> resources = null
        String namespace
        if (resourceDtl != null) {
            //first check if the chapter is already setup
            namespace = resourceDtl.chapterId + "_" + resourceDtl.id
            request.setAttribute("gptAdmin", "true")
            status = "uploaded"

            List resList = GptDefaultCreateLog.findAllByReadingMaterialResId(resourceDtl.id)
            //collect promptType,id to resources list
            resources = resList.collect { resource ->
                return [id         : resource.id,
                        promptType : resource.promptType,
                        promptLabel: resource.promptLabel,
                ]
            }
        } else {
            status = "Resource not found"
        }
        def json = [status: status, readingMaterialResId: resourceDtl != null ? resourceDtl.id : null, createdResources: resources, namespace: namespace]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def createAndUpdateGPTContent() {
        println("***** reallly did it not compile and lets try again")
        if (params.readingMaterialResId == null) {
            ResourceDtl rd = ResourceDtl.findByChapterIdAndResTypeAndGptResourceTypeIsNull(new Integer(params.chapterId), "Notes")
            params.readingMaterialResId = "" + rd.id
            params.namespace = params.chapterId + "_" + rd.id
        }
        println("params: " + params)
        Prompts prompt = Prompts.findByPromptType(params.promptType)
        boolean createResource = true
        if ("true".equals(redisService.get("chapter_" + params.readingMaterialResId + "_" + params.promptType))) {
            def json = [response: "In process"]
            render json as JSON
        } else {
            //create a key in redis chaptername and prompt type and it should be there for 20 minutes
            if (prompt == null) {
                def json = [response: "Prompt not found"]
                render json as JSON
            } else {
                if ("mcqs".equals(prompt.parentPromptType) || "mcq".equals(prompt.parentPromptType) || "qna".equals(prompt.parentPromptType) || "pns".equals(prompt.parentPromptType)) {
                    ResourceDtl readingMaterialResourceDtl = dataProviderService.getResourceDtl(new Integer(params.readingMaterialResId))
                    if (readingMaterialResourceDtl.mcqTypes != null) {
                        if (readingMaterialResourceDtl.mcqTypes.contains(prompt.promptType)) {
                            createResource = false
                            def json = [response: "Created"]
                            render json as JSON
                        }
                    }
                    if (readingMaterialResourceDtl.qaTypes != null) {
                        if (readingMaterialResourceDtl.qaTypes.contains(prompt.promptType)) {
                            createResource = false
                            def json = [response: "Created"]
                            render json as JSON
                        }
                    }

                }
                if (createResource) {
                    if ("zcleanup".equals(params.promptType)) {
                        String response = "MCQs:"
                        ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndGptResourceTypeIsNotNull(new Integer(params.chapterId), "Multiple Choice Questions")
                        if (resourceDtl != null)
                            response += deleteDuplicateQuestions(resourceDtl) + "<br>QnA:"
                        resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndGptResourceTypeIsNotNull(new Integer(params.chapterId), "QA")
                        if (resourceDtl != null)
                            response += deleteDuplicateQuestions(resourceDtl)
                        def json = [response: response]
                        render json as JSON
                    } else {
                        String redisKey = "chapter_" + params.readingMaterialResId + "_" + params.promptType
                        redisService.set(redisKey, "true")
                        redisService.expire(redisKey, 1200)
                        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Integer(params.readingMaterialResId))
                        def returnValue = promptService.createAndUpdateGPTContent(params.namespace, params.readingMaterialResId, request, prompt, session)
                        redisService.set(redisKey, "done")
                        gptLogService.getGPTResources(new Long(params.readingMaterialResId))
                        def json = [response: returnValue]
                        render json as JSON
                    }
                }
            }
        }

    }

    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def autoAddGptContent() {
        def requestBody = request.JSON
        String returnResponse = promptService.autoAddGptContent(session, requestBody.resType, requestBody.readingMaterialResId, request, requestBody, requestBody.promptLabel, requestBody.query, requestBody.resType)
        String redisKey = "chapter_" + requestBody.readingMaterialResId + "_" + requestBody.promptType
        redisService.set(redisKey, "done")
        gptLogService.getGPTResources(new Long("" + requestBody.readingMaterialResId))
        def json = [response: returnResponse]
        render json as JSON
    }

    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def promptTemplateManager() {
        List prompts = Prompts.findAll([sort: [parentPromptType: 'asc', promptLabel: 'asc']])
        List promptTemplates = PromptTemplateMst.findAll([sort: [name: 'asc']])
        [prompts: prompts, title: "Prompt Manager", promptTemplates: promptTemplates]
    }

    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def addPromptTemplate() {
        PromptTemplateMst promptTemplateMst = new PromptTemplateMst(name: params.templateName, siteId: new Integer("" + session["siteId"]))
        promptTemplateMst.save(flush: true)
        String[] promptIds = params.promptIds.split(",")
        promptIds.each { promptId ->
            Prompts prompt = Prompts.findByPromptType(promptId)
            PromptTemplateDtl promptTemplateDtl = new PromptTemplateDtl(templateId: promptTemplateMst.id, promptType: promptId)
            promptTemplateDtl.save(flush: true)
        }
        def json = [response: "success"]
        render json as JSON
    }

    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def editPromptTemplate() {
        PromptTemplateMst promptTemplateMst = PromptTemplateMst.get(new Integer(params.templateId))
        promptTemplateMst.name = params.templateName
        promptTemplateMst.save(flush: true)
        PromptTemplateDtl.executeUpdate("delete from PromptTemplateDtl where templateId = ?", new Integer("" + promptTemplateMst.id))
        String[] promptIds = params.promptIds.split(",")
        promptIds.each { promptId ->
            PromptTemplateDtl promptTemplateDtl = new PromptTemplateDtl(templateId: promptTemplateMst.id, promptType: promptId)
            promptTemplateDtl.save(flush: true)
        }
        def json = [response: "success"]
        render json as JSON
    }

    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def deletePromptTemplate() {
        PromptTemplateMst promptTemplateMst = PromptTemplateMst.get(new Integer(params.templateId))
        PromptTemplateDtl.executeUpdate("delete from PromptTemplateDtl where templateId = ?", new Integer("" + promptTemplateMst.id))
        promptTemplateMst.delete(flush: true)
        def json = [response: "success"]
        render json as JSON
    }

    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def getTemplateDetails() {
        PromptTemplateMst promptTemplateMst = PromptTemplateMst.get(new Integer(params.templateId))
        List promptTemplateDtls = PromptTemplateDtl.findAllByTemplateId(new Integer(params.templateId))
        //get the comma separated promptType
        String prompts = promptTemplateDtls.collect { promptTemplateDtl ->
            return promptTemplateDtl.promptType
        }.join(",")
        def json = [templateName: promptTemplateMst.name, prompts: prompts]
        render json as JSON
    }

    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def copyChapters() {
        String[] bookIds = params.bookIds.split(",")
        println("bookIds: " + bookIds)
        //loop bookIds
        bookIds.each { bookId ->
            println("bookId: " + bookId)
            List chapters = ChaptersMst.findAllByBookId(new Integer(bookId))
            chapters.each { chapter ->
                resourceCreatorService.copyChapter("" + chapter.id, "" + params.destBookId, new Integer("" + session["siteId"]))
            }
            resourceCreatorService.copyGptContents("" + params.destBookId)
        }
        def json = [response: "success"]
        render json as JSON
    }

    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def deleteDuplicateQuestions(ResourceDtl resourceDtl) {
        try {
            Prompts prompts = Prompts.findByPromptType("zcleanup")
            List questions = ObjectiveMst.findAllByQuizId(new Integer("" + resourceDtl.resLink))
            int initialQuestions = questions.size()
            def file = new File("duplicateQuestions" + resourceDtl.resLink + ".txt");
            file.append("[")
            //loop through the questions
            questions.each { question ->
                //get the question
                String questionText = question.question
                file.append("\"" + question.id + "~" + questionText.replace('\"', '') + "\",")
            }
            //remove the last comma
            file.text = file.text.substring(0, file.text.length() - 1)
            file.append("]")

            URL url = new URL(promptService.getGPTServerUrl(request) + "/retrieveDataAdminText")
            HttpURLConnection conn = (HttpURLConnection) url.openConnection()
            conn.setRequestMethod("POST")

            String boundary = "===" + System.currentTimeMillis() + "==="
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary)
            conn.setDoOutput(true)

            String prompt = prompts.basePrompt
            try {
                OutputStream output = conn.getOutputStream();
                PrintWriter writer = new PrintWriter(new OutputStreamWriter(output, "UTF-8"), true)
                writer.append("\r\n").flush()
                writer.append("--" + boundary).append("\r\n")
                writer.append("Content-Disposition: form-data; name=\"prompt\"").append("\r\n")
                writer.append("Content-Type: text/plain; charset=UTF-8").append("\r\n")
                writer.append("\r\n").flush()
                output.write(prompt.getBytes("UTF-8"))
                output.flush()

                // Send file.
                writer.append("\r\n").flush()
                writer.append("--" + boundary).append("\r\n")
                writer.append("Content-Disposition: form-data; name=\"file\"; filename=\"file.txt\"").append("\r\n")
                writer.append("Content-Type: text/plain; charset=UTF-8").append("\r\n")
                writer.append("\r\n").flush()
                output.write(file.text.getBytes("UTF-8"))
                output.flush()

                // End of multipart/form-data.
                writer.append("\r\n").flush()
                writer.append("--" + boundary + "--").append("\r\n").flush()
            }
            catch (Exception e) {
                e.printStackTrace()
            }

            String response = conn.getInputStream().getText()

            def json = new JsonSlurper().parseText(response)
            String responseAnswer = json.response
            responseAnswer = responseAnswer.replaceAll("`", "")
            responseAnswer = responseAnswer.replaceAll("json", "")
            responseAnswer = responseAnswer.replaceAll("\n", "")
            responseAnswer = responseAnswer.replaceAll("\r", "")
            //next i want to replace ][ with comma
            responseAnswer = responseAnswer.replaceAll("\\]\\[", ",")
            // replace },] with }]
            responseAnswer = responseAnswer.replaceAll("},]", "}]")

            //replace  .^ with blank
            responseAnswer = responseAnswer.replaceAll("\\.\\^", "")


            def questionsList = new JsonSlurper().parseText(responseAnswer)

            String outputQuestions = ""
            //loop questionsList
            questionsList.each { question ->
                println(question)
                //get the question id
                String[] questionIds = question.ids.split(",")
                if (questionIds.size() > 1) {
                    //loop questionIds
                    outputQuestions += questionIds[0] + ","
                    String ids = question.ids.substring(question.ids.indexOf(",") + 1)
                    ObjectiveMst.executeUpdate("delete from ObjectiveMst where id in (" + ids + ")")
                }
            }

            //delete the file
            file.delete()
            redisService.("quiz_" + resourceDtl.id) = null
            questions = ObjectiveMst.findAllByQuizId(new Integer("" + resourceDtl.resLink))
            return "Original Questions: " + initialQuestions + "\nLeft Questions: " + questions.size()
        } catch (Exception e) {
            return "Exception happened: " + e.getMessage()
        }
    }

    def duplicateFixer() {

    }

    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def updateMissingResources() {
        String bookId = params.bookId
        List chapters = ChaptersMst.findAllByBookId(new Integer(bookId))
        int count = 0
        chapters.each { chapter ->
            ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndGptResourceTypeIsNull(new Integer("" + chapter.id), "Notes")
            if (resourceDtl != null) {
                List resources = ResourceDtl.findAllByChapterIdAndGptResourceTypeIsNotNull(new Integer("" + chapter.id))
                resources.each { resource ->
                    if (resource.parentId != null) {
                        GptDefaultCreateLog newGptLog = GptDefaultCreateLog.findByResId(resource.id)
                        if (newGptLog == null) {
                            GptDefaultCreateLog gptLog = GptDefaultCreateLog.findByResId(resource.parentId)
                            //create new GptDefaultCreateLog and copy the content
                            if (gptLog != null) {
                                newGptLog = new GptDefaultCreateLog(username: gptLog.username, prompt: gptLog.prompt, response: gptLog.response, response2: gptLog.response2, promptType: gptLog.promptType, resId: resource.id, readingMaterialResId: resourceDtl.id, promptLabel: gptLog.promptLabel)
                                newGptLog.save(flush: true)
                                count++
                            }
                        }
                    }
                }
                gptLogService.getGPTResources(new Long("" + resourceDtl.id))
            }

        }
        render "total " + count
    }

    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def getChapterDetails() {
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        if (redisService.("allChapterDetails_" + params.bookId) == null) {
            dataProviderService.getAllChapterDetails(new Long(params.bookId))
        }
        List chaptersList = new JsonSlurper().parseText(redisService.("allChapterDetails_" + params.bookId))
        List chapterDetails = chaptersList.collect { it ->
            if (it != null) {
                if (it.resType == "Notes" && it.link.endsWith(".pdf") && it.link != "blank") {
                    return it
                }
            }
        }.findAll { it != null }
        chapterDetails = chapterDetails.sort { it.chapterId }
        def json = [bookTitle: booksMst.title, chapterDetails: chapterDetails]
        render json as JSON
    }

    @Secured(['ROLE_GPT_MANAGER'])
    def kindleTemplate() {

    }

    @Secured(['ROLE_USER'])
    @Transactional
    def getTeachingCoachLevel2() {
        String chapterId = params.chapterId
        ResourceDtl readingMaterialResourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
        ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndGptResourceType(new Integer(chapterId), "teaching_coach")
        String response
        if (resourceDtl != null) {
            FlowSegment flowSegment = FlowSegment.findByResourceDtlIdAndSlug(new Long("" + resourceDtl.id), request.getParameter("slug"))
            if (flowSegment == null) {
                Prompts prompt = Prompts.findByPromptType("teaching_coach_slug")
                String query = prompt.basePrompt.replace("slug_type", request.getParameter("slug"))
                def jsonResponse = promptService.runPrompt(readingMaterialResourceDtl.resLink, params.chapterId + "_" + params.resId, query, "teaching_coach_slug", request)
                flowSegment = new FlowSegment(resourceDtlId: resourceDtl.id, slug: request.getParameter("slug"), detailJson: jsonResponse.answer)
                response = jsonResponse.answer
                flowSegment.save(failOnError: true, flush: true)
            } else {
                response = flowSegment.detailJson
            }
        }
        // if response is not null and if it is enclosed with ```json and ``` then remove it
        if (response != null) {
            if (response.startsWith("```json") && response.endsWith("```")) {
                response = response.substring(8, response.length() - 3)
            }
        }
        def json = [response: response]
        render json as JSON
    }

    @Transactional
    def exerciseCollector() {
        def json = autogptService.exerciseCollector(params)
        render json as JSON
    }


    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def getChapterMetaData() {
        def json = autogptService.getChapterMetaData(params)
        render json as JSON
    }

    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def questionBankBuilder() {
        def json = autogptService.questionBankBuilder(params)
        render json as JSON
    }


    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def autogptTask() {
        List availableChapters = ChaptersMst.findAllByBookId(new Integer(params.bookId))
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        [availableChapters: availableChapters, bookTitle: booksMst.title, title: "AutoGPT Task Manager"]
    }

    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def addToAutoGptTask() {
        String[] chapterIds = params.chapterIds.split(",")
        String createSnapshot = params.createSnapshot

        for (int i = 0; i < chapterIds.length; i++) {
            //check if chapter id exists
            AutogptLog autogptLog = AutogptLog.findByChapterId(new Long(chapterIds[i]))
            if (autogptLog == null) {
                autogptLog = new AutogptLog(chapterId: new Long(chapterIds[i]), createdBy: springSecurityService.currentUser.username,
                        createSnapshot: createSnapshot)
                autogptLog.save(failOnError: true, flush: true)
            }
        }
        def json = [status: "OK"]
        render json as JSON

    }

    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def getAutoGptTask() {
        List autogptLogs = AutogptLog.findAllByDateCompletedIsNull()
        render autogptLogs as JSON
    }

    def test() {

    }

    def jsonValidator() {
        String inputText = params.inputText
        try {
            new JsonSlurper().parseText(autogptService.fixJsonString(inputText))
            def json = [message: "Valid JSON"]
            render json as JSON
        } catch (Exception e) {
            def json = [message: "Invalid JSON"]
            render json as JSON
        }
    }

    @Transactional

    def runAutoGPT(int chapterId) {
        def params = new HashMap()
        params.put("chapterId",""+chapterId)
        ResourceDtl resourceDtl
        List readingMaterials = ResourceDtl.findAllByChapterIdAndResTypeAndSharingIsNullAndGptResourceTypeIsNull(new Integer(params.chapterId),"Notes", [sort: "id", order: "asc"])
        if(readingMaterials.size()>0) {
            params.put("resId", "" + readingMaterials[0].id)
            resourceDtl = readingMaterials[0]
        }
        else {
            def json = [status: "Error", message: "No PDF found for this chapter"]
            return json
        }

        println("calling storePdfVectors")
        def json1 = autogptService.storePdfVectors(params)
        println("json1: "+json1)
        println("calling getChapterMetaData")
        autogptService.getChapterMetaData(params)
        println("calling exerciseCollector")
        autogptService.exerciseCollector(params)
        println("calling questionBankBuilder")
        autogptService.questionBankBuilder(params)
        println("calling deleteEmbeddings")
        autogptService.deleteEmbeddings(resourceDtl)

        //check if this chapter is in autogptLog and if it is then update the status to completed
        AutogptLog autogptLog = AutogptLog.findByChapterId(new Long(chapterId))
        if(autogptLog!=null) {
            autogptLog.gptStatus = "completed"
            autogptLog.dateCompleted = new Date()
            autogptLog.save(failOnError: true, flush: true)
        }
        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(new Long(chapterId))
        dataProviderService.getChaptersList(chaptersMst.bookId)
        def json = [status:"OK",message:"AutoGPT task completed"]
        return json
    }

    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def storePdfVectors() {
        def json = autogptService.storePdfVectors(params)
        render json as JSON
    }

    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def autoGPTAdmin() {
        [title: "AutoGPT Admin"]
    }



    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def getAutoGPTAdminData() {
        def status = params.status ?: 'running'
        def page = params.page ? Integer.parseInt(params.page) : 0
        def pageSize = params.pageSize ? Integer.parseInt(params.pageSize) : 50
        def offset = page * pageSize

        try {
            List<AutogptLog> autogptLogs = []
            def totalCount = 0

            // Filter based on status
            switch (status) {
                case 'running':
                    autogptLogs = AutogptLog.findAllByGptStatus('running', [max: pageSize, offset: offset, sort: 'dateCreated', order: 'desc'])
                    totalCount = AutogptLog.countByGptStatus('running')
                    break
                case 'completed':
                    autogptLogs = AutogptLog.findAllByGptStatus('completed', [max: pageSize, offset: offset, sort: 'dateCreated', order: 'desc'])
                    totalCount = AutogptLog.countByGptStatus('completed')
                    break
                case 'not_yet_started':
                    autogptLogs = AutogptLog.findAllByGptStatusIsNull([max: pageSize, offset: offset, sort: 'dateCreated', order: 'desc'])
                    totalCount = AutogptLog.countByGptStatusIsNull()
                    break
                default:
                    autogptLogs = AutogptLog.findAll([max: pageSize, offset: offset, sort: 'dateCreated', order: 'desc'])
                    totalCount = AutogptLog.count()
            }

            // Convert to result list with chapter and book information
            List resultList = []
            autogptLogs.each { log ->
                def result = [:]
                result.id = log.id
                result.chapterId = log.chapterId
                result.gptStatus = log.gptStatus
                result.createdBy = log.createdBy
                result.attempts = log.attempts
                result.createSnapshot = log.createSnapshot

                // Convert GMT dates to IST (add 5:30 hours)
                if (log.dateCreated) {
                    Calendar cal = Calendar.getInstance()
                    cal.setTime(log.dateCreated)
                    cal.add(Calendar.HOUR_OF_DAY, 5)
                    cal.add(Calendar.MINUTE, 30)
                    result.dateAdded = formatDateToIST(cal.getTime())
                }

                if (log.dateStarted) {
                    Calendar cal = Calendar.getInstance()
                    cal.setTime(log.dateStarted)
                    cal.add(Calendar.HOUR_OF_DAY, 5)
                    cal.add(Calendar.MINUTE, 30)
                    result.dateStarted = formatDateToIST(cal.getTime())
                }

                if (log.dateCompleted) {
                    Calendar cal = Calendar.getInstance()
                    cal.setTime(log.dateCompleted)
                    cal.add(Calendar.HOUR_OF_DAY, 5)
                    cal.add(Calendar.MINUTE, 30)
                    result.dateCompleted = formatDateToIST(cal.getTime())
                }

                // Calculate time taken if both start and end dates exist
                if (log.dateStarted && log.dateCompleted) {
                    long timeTakenMs = log.dateCompleted.getTime() - log.dateStarted.getTime()
                    result.timeTaken = formatDuration(timeTakenMs)
                } else {
                    result.timeTaken = '-'
                }

                // Get chapter name and book title from wscontent schema
                try {
                    ChaptersMst chapter = ChaptersMst.findById(log.chapterId)
                    if (chapter) {
                        result.chapterName = chapter.name

                        BooksMst book = BooksMst.findById(chapter.bookId)
                        if (book) {
                            result.bookTitle = book.title
                        } else {
                            result.bookTitle = 'Unknown Book'
                        }
                    } else {
                        result.chapterName = 'Unknown Chapter'
                        result.bookTitle = 'Unknown Book'
                    }
                } catch (Exception e) {
                    log.error("Error fetching chapter/book data for chapterId: ${log.chapterId}", e)
                    result.chapterName = 'Error loading'
                    result.bookTitle = 'Error loading'
                }

                resultList.add(result)
            }

            def json = [
                    status     : 'OK',
                    data       : resultList,
                    totalCount : totalCount,
                    currentPage: page,
                    pageSize   : pageSize,
                    totalPages : Math.ceil(totalCount / pageSize)
            ]
            render json as JSON

        } catch (Exception e) {
            log.error("Error in getAutoGPTAdminData", e)
            def json = [status: 'ERROR', message: 'Error fetching data: ' + e.message]
            render json as JSON
        }
    }

    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def deleteAutoGPTLog() {
        try {
            def logId = params.id
            if (!logId) {
                def json = [status: 'ERROR', message: 'Log ID is required']
                render json as JSON
                return
            }

            AutogptLog autogptLog = AutogptLog.findById(Long.parseLong(logId))
            if (!autogptLog) {
                def json = [status: 'ERROR', message: 'AutoGPT log not found']
                render json as JSON
                return
            }

            // Only allow deletion if status is null (not yet started)
            if (autogptLog.gptStatus != null) {
                def json = [status: 'ERROR', message: 'Can only delete chapters that are not yet started']
                render json as JSON
                return
            }

            autogptLog.delete(flush: true)
            def json = [status: 'OK', message: 'AutoGPT log deleted successfully']
            render json as JSON

        } catch (Exception e) {
            log.error("Error in deleteAutoGPTLog", e)
            def json = [status: 'ERROR', message: 'Error deleting log: ' + e.message]
            render json as JSON
        }
    }

    private String formatDateToIST(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("dd-MMM-yyyy HH:mm:ss")
        return sdf.format(date)
    }

    private String formatDuration(long milliseconds) {
        long seconds = milliseconds / 1000
        long hours = seconds / 3600
        long minutes = (seconds % 3600) / 60
        long secs = seconds % 60

        if (hours > 0) {
            return String.format("%02d:%02d:%02d", hours, minutes, secs)
        } else {
            return String.format("%02d:%02d", minutes, secs)
        }
    }


    @Transactional
    def autoGPTRunner() {
        int chapterId = autogptService.getNextChapterForProcessing()
        if(chapterId>0) {
            runAutoGPT(chapterId)
        }
        render "Completed"
    }



    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def getChapterResources() {
        def json = [resources: autogptService.getChapterResources(params.bookId)]
        render json as JSON
    }

    @Transactional
    def setGPTOnOff(){
        KeyValueMst runGPTJob = KeyValueMst.findByKeyName("runGPTJob")
        runGPTJob.keyValue = params.runGPTJob
        runGPTJob.save(failOnError: true, flush: true)
        def json = [status: "OK"]
        render json as JSON
    }

    @Transactional
    def checkPendingJobs(){
        autogptService.checkPendingJobs()
        def json = [status: "OK"]
        render json as JSON
    }


}






