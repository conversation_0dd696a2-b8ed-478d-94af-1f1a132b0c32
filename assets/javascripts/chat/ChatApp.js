class ChatApp {
    constructor() {
        this.isToggleActive = true; // Default state is active
        this.isMobile = window.innerWidth <= 1024;
        this.typingWorker = null;

        // Initialize on DOM ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.init();
            });
        } else {
            this.init();
        }
    }

    init() {
        this.setDefaultState();
        this.handleResize();
        this.setupChatEventListeners();

        // Listen for window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    setDefaultState() {
        const toggleBtn = document.getElementById('toggle-btn');
        const toggleLabel = document.querySelector('.toggle-label');

        if (toggleBtn) {
            // Set toggle as active by default
            toggleBtn.classList.add('active');
        }

        if (toggleLabel) {
            toggleLabel.classList.add('toggle-label-color');
        }

        // Set initial panel states
        this.updatePanelStates();
    }

    handleResize() {
        this.isMobile = window.innerWidth <= 1024;
        this.updatePanelStates();
    }

    updatePanelStates() {
        const leftPanel = document.getElementById('left-panel');
        const rightPanel = document.getElementById('right-panel');
        const divider = document.getElementById('divider');
        const chatApp = document.getElementById('chat-app');

        if (!leftPanel || !rightPanel || !divider || !chatApp) return;

        // Reset all classes
        leftPanel.className = 'chat-panel left-panel';
        rightPanel.className = 'chat-panel right-panel';
        divider.className = 'divider';
        chatApp.classList.remove('mobile-right-panel-active');

        if (this.isMobile) {
            // Mobile/Tablet behavior
            if (this.isToggleActive) {
                // Show left panel full screen
                leftPanel.classList.add('left-panel-full');
                rightPanel.classList.add('right-panel-hidden');
                divider.classList.add('divider-hidden');
            } else {
                // Show right panel full screen
                chatApp.classList.add('mobile-right-panel-active');
            }
        } else {
            // Desktop behavior
            if (this.isToggleActive) {
                // Left panel takes full width
                leftPanel.classList.add('left-panel-full');
                rightPanel.classList.add('right-panel-hidden');
                divider.classList.add('divider-hidden');
            } else {
                // Split view - both panels visible
                leftPanel.classList.add('left-panel-half');
                rightPanel.classList.add('right-panel-visible');
            }
        }
    }

    /**
     * Setup chat event listeners for send button and enter key
     */
    setupChatEventListeners() {
        const sendButton = document.getElementById('send-button');
        const chatInput = document.querySelector('input[name="chat"]');

        if (sendButton && chatInput) {
            // Send button click event
            sendButton.addEventListener('click', () => {
                this.handleSendMessage();
            });

            // Enter key press event
            chatInput.addEventListener('keypress', (event) => {
                if (event.key === 'Enter') {
                    event.preventDefault();
                    this.handleSendMessage();
                }
            });
        }
    }

    /**
     * Handle sending a message
     */
    handleSendMessage() {
        const chatInput = document.querySelector('input[name="chat"]');
        const chatMessages = document.getElementById('chat-messages');

        if (chatInput && chatMessages) {
            const message = chatInput.value.trim();
            if (message) {
                // Add user message
                this.addMessage('user', message, chatMessages);

                // Clear input
                chatInput.value = '';

                this.showTypingLoader(chatMessages)

                // Simulate bot response (you can replace this with actual bot logic)
                setTimeout(() => {
                    this.hideTypingLoader()
                    this.addMessage('bot', 'This is a simulated bot response to: \\(6 \\sqrt{6}\\) "' + message + '"', chatMessages);
                }, 3000);
            }
        }
    }

    /**
     * Reusable method to add messages to chat
     * @param {string} type - Message type ('user' or 'bot')
     * @param {string} content - Message content
     * @param {HTMLElement} target - Target container element
     */
    addMessage(type, content, target) {
        if (!target || !content || !type) {
            console.error('addMessage: Missing required parameters', `Required parameters: ${type}, ${content}, ${target}`);
            return;
        }

        // Validate type
        if (type !== 'user' && type !== 'bot') {
            console.error('addMessage: Invalid type. Must be "user" or "bot"');
            return;
        }

        // Create message element
        const messageElement = document.createElement('div');
        messageElement.className = `chat-message ${type}`;

        // Create message content
        const contentElement = document.createElement('div');
        contentElement.className = 'chat-message-content';

        // For bot messages, don't set content immediately (typewriter will handle it)
        // For user messages, set content immediately
        if (type === 'user') {
            contentElement.textContent = content;
        }

        // Append elements
        messageElement.appendChild(contentElement);
        target.appendChild(messageElement);

        // Scroll to bottom
        target.scrollTop = target.scrollHeight;

        if (type === 'bot') {
            this.typeWriter(content, 0, null, target);
        }
    }

    /**
     * Show typing loader
     */

    showTypingLoader(target){
        // Create the container
        const isTyping = document.createElement('div');
        isTyping.className = 'is-typing';

        ['jump1', 'jump2', 'jump3'].forEach(cls => {
            const div = document.createElement('div');
            div.className = cls;
            isTyping.appendChild(div);
        });
        target.appendChild(isTyping); // Change to your specific container
        target.scrollTop = target.scrollHeight;
    }

    /**
     * Hide typing loader
     */

    hideTypingLoader(){
        const isTyping = document.querySelector('.is-typing');
        if (!isTyping) {
            console.error('Typing loader is not present');
            return;
        }
        isTyping.remove()
    }


    /**
     * Typing effect for bot message
     */

    typeWriter(text, i, callback, targetContainer){
        // Terminate existing worker if any
        if (this.typingWorker) {
            this.typingWorker.terminate();
            this.typingWorker = null;
        }

        // Find the last bot message element to update
        const chatMessages = targetContainer
        if (!chatMessages) {
            console.error('Chat messages container not found');
            return;
        }

        const botMessages = chatMessages.querySelectorAll('.chat-message.bot .chat-message-content');
        const lastBotMessage = botMessages[botMessages.length - 1];

        if (!lastBotMessage) {
            console.error('No bot message found to animate');
            return;
        }

        // Clear the content initially
        lastBotMessage.textContent = '';

        try {
            this.typingWorker = new Worker('/assets/bookGPTScripts/typeWorker.js');

            this.typingWorker.postMessage({ text: text, index: i, speed: 10 });

            this.typingWorker.onmessage = (e) => {
                if (e.data.done) {
                    // Animation complete
                    this.typingWorker.terminate();
                    this.typingWorker = null;
                    if (callback) {
                        callback();
                    }
                    this.handleFormula(text, lastBotMessage)
                } else {
                    // Update the displayed text
                    this.handleFormula(text.substring(0, e.data.index), lastBotMessage)
                }
                // Auto-scroll to bottom
                chatMessages.scrollTop = chatMessages.scrollHeight;
            };

            // Handle worker errors
            this.typingWorker.onerror = (error) => {
                console.error('Worker error:', error);
                this.typingWorker.terminate();
                this.typingWorker = null;
                // Fallback: show full text immediately
                lastBotMessage.textContent = text;
                if (callback) {
                    callback();
                }
            };

        } catch (error) {
            console.error('Failed to create worker:', error);
            // Fallback: show full text immediately
            lastBotMessage.textContent = text;
            if (callback) {
                callback();
            }
        }
    }

    /**
     * Show formula using katex
     */
    handleFormula(currentText, lastBotMessage){
        const tempElement = document.createElement("div");
        tempElement.innerHTML = currentText;

        renderMathInElement(tempElement, {
            delimiters: [
                { left: "\\(", right: "\\)", display: false },
                { left: "\\[", right: "\\]", display: true }
            ]
        });
        lastBotMessage.innerHTML = marked.parse(tempElement.innerHTML);
    }

    /**
     * Toggle functionality for buttons
     * @param {string} toggleId - The ID of the toggle button
     * @returns {Function} - Event handler function
     */
    toggle(toggleId) {
        return () => {
            const toggleBtn = document.getElementById(toggleId);
            const toggleLabel = document.querySelector('.toggle-label');

            if (toggleBtn) {
                // Toggle the button's active state
                this.isToggleActive = !this.isToggleActive;

                if (this.isToggleActive) {
                    toggleBtn.classList.add('active');
                    if (toggleLabel) {
                        toggleLabel.classList.add('toggle-label-color');
                    }
                } else {
                    toggleBtn.classList.remove('active');
                    if (toggleLabel) {
                        toggleLabel.classList.remove('toggle-label-color');
                    }
                }

                // Update panel states based on new toggle state
                this.updatePanelStates();
            }
        };
    }
}