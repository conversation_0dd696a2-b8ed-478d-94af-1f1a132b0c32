/**
 * ResizableDivider - A reusable class for creating resizable panel dividers
 * Allows users to drag a divider to resize adjacent panels
 */
class ResizableDivider {
    /**
     * Constructor for ResizableDivider
     * @param {Object} options - Configuration options
     * @param {string} options.dividerId - ID of the divider element
     * @param {string} options.leftPanelId - ID of the left panel element
     * @param {string} options.rightPanelId - ID of the right panel element
     * @param {string} options.containerId - ID of the container element
     * @param {number} options.minPanelSize - Minimum size for panels (default: 200px)
     * @param {boolean} options.enabled - Whether the divider is initially enabled (default: true)
     */
    constructor(options = {}) {
        // Configuration
        this.dividerId = options.dividerId || 'divider';
        this.leftPanelId = options.leftPanelId || 'left-panel';
        this.rightPanelId = options.rightPanelId || 'right-panel';
        this.containerId = options.containerId || 'panels-container';
        this.minPanelSize = options.minPanelSize || 200;
        this.enabled = options.enabled !== false;

        // State variables
        this.isDragging = false;
        this.startX = 0;
        this.startLeftWidth = 0;
        this.startRightWidth = 0;
        this.containerWidth = 0;
        this.customSizes = null; // Store custom panel sizes

        // DOM elements (will be set in init)
        this.divider = null;
        this.leftPanel = null;
        this.rightPanel = null;
        this.container = null;

        // Bind methods to preserve 'this' context
        this.handleMouseDown = this.handleMouseDown.bind(this);
        this.handleMouseMove = this.handleMouseMove.bind(this);
        this.handleMouseUp = this.handleMouseUp.bind(this);
        this.handleResize = this.handleResize.bind(this);

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.init();
            });
        } else {
            this.init();
        }
    }

    /**
     * Initialize the resizable divider
     */
    init() {
        // Get DOM elements
        this.divider = document.getElementById(this.dividerId);
        this.leftPanel = document.getElementById(this.leftPanelId);
        this.rightPanel = document.getElementById(this.rightPanelId);
        this.container = document.getElementById(this.containerId);

        // Validate elements exist
        if (!this.divider || !this.leftPanel || !this.rightPanel || !this.container) {
            console.error('ResizableDivider: Required elements not found', {
                divider: this.divider ? 'found' : `missing (${this.dividerId})`,
                leftPanel: this.leftPanel ? 'found' : `missing (${this.leftPanelId})`,
                rightPanel: this.rightPanel ? 'found' : `missing (${this.rightPanelId})`,
                container: this.container ? 'found' : `missing (${this.containerId})`
            });
            return;
        }

        // Set up event listeners
        this.setupEventListeners();

        // Set initial state
        this.updateState();

        console.log('ResizableDivider: Successfully initialized');
    }

    /**
     * Set up event listeners for the divider
     */
    setupEventListeners() {
        // Mouse events for dragging
        this.divider.addEventListener('mousedown', this.handleMouseDown);

        // Global mouse events (attached to document for better tracking)
        document.addEventListener('mousemove', this.handleMouseMove);
        document.addEventListener('mouseup', this.handleMouseUp);

        // Window resize event
        window.addEventListener('resize', this.handleResize);

        // Prevent text selection during drag
        this.divider.addEventListener('selectstart', (e) => e.preventDefault());
    }

    /**
     * Handle mouse down event on divider
     */
    handleMouseDown(event) {
        // Only proceed if divider is enabled and visible
        if (!this.enabled || !this.isDividerVisible()) {
            return;
        }

        event.preventDefault();

        this.isDragging = true;
        this.startX = event.clientX;

        // Get current panel widths
        const leftRect = this.leftPanel.getBoundingClientRect();
        const rightRect = this.rightPanel.getBoundingClientRect();
        const containerRect = this.container.getBoundingClientRect();

        this.startLeftWidth = leftRect.width;
        this.startRightWidth = rightRect.width;
        this.containerWidth = containerRect.width;

        // Add dragging class for visual feedback
        this.divider.classList.add('dragging');
        document.body.style.cursor = 'col-resize';
        document.body.style.userSelect = 'none';
    }

    /**
     * Handle mouse move event during dragging
     */
    handleMouseMove(event) {
        if (!this.isDragging || !this.enabled) {
            return;
        }

        event.preventDefault();

        const deltaX = event.clientX - this.startX;
        const newLeftWidth = this.startLeftWidth + deltaX;
        const newRightWidth = this.startRightWidth - deltaX;

        // Check minimum size constraints
        if (newLeftWidth < this.minPanelSize || newRightWidth < this.minPanelSize) {
            return;
        }

        // Update panel sizes
        this.updatePanelSizes(newLeftWidth, newRightWidth);
    }

    /**
     * Handle mouse up event to end dragging
     */
    handleMouseUp(event) {
        if (!this.isDragging) {
            return;
        }

        this.isDragging = false;

        // Remove dragging visual feedback
        this.divider.classList.remove('dragging');
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
    }

    /**
     * Update panel sizes with given widths
     */
    updatePanelSizes(leftWidth, rightWidth) {
        const totalWidth = leftWidth + rightWidth;
        const leftPercentage = (leftWidth / totalWidth) * 100;
        const rightPercentage = (rightWidth / totalWidth) * 100;

        // Store custom sizes
        this.customSizes = {
            leftPercentage,
            rightPercentage
        };

        // Use flex-basis for better responsive behavior
        this.leftPanel.style.flexBasis = `${leftPercentage}%`;
        this.rightPanel.style.flexBasis = `${rightPercentage}%`;

        // Ensure flex-grow and flex-shrink are set appropriately
        this.leftPanel.style.flexGrow = '0';
        this.leftPanel.style.flexShrink = '0';
        this.rightPanel.style.flexGrow = '0';
        this.rightPanel.style.flexShrink = '0';
    }

    /**
     * Check if divider should be visible and interactive
     */
    isDividerVisible() {
        // Check if divider is visible (not hidden by CSS)
        const dividerStyle = window.getComputedStyle(this.divider);
        return dividerStyle.display !== 'none';
    }

    /**
     * Handle window resize events
     */
    handleResize() {
        // Reset panel sizes to default flex behavior on resize
        // This ensures responsive behavior is maintained
        if (this.leftPanel && this.rightPanel) {
            this.resetPanelSizes();
        }
    }

    /**
     * Reset panel sizes to default flex behavior
     */
    resetPanelSizes() {
        if (this.leftPanel && this.rightPanel) {
            this.leftPanel.style.flexBasis = '';
            this.leftPanel.style.flexGrow = '';
            this.leftPanel.style.flexShrink = '';
            this.rightPanel.style.flexBasis = '';
            this.rightPanel.style.flexGrow = '';
            this.rightPanel.style.flexShrink = '';
        }
    }

    /**
     * Update the state of the divider (called externally when panel visibility changes)
     */
    updateState() {
        // This method can be called by external code (like ChatApp)
        // when panel visibility changes due to toggle functionality
        if (!this.isDividerVisible()) {
            this.resetPanelSizes();
        } else if (this.customSizes && this.isDividerVisible()) {
            // Restore custom sizes if divider becomes visible again
            this.restoreCustomSizes();
        }
    }

    /**
     * Restore previously set custom panel sizes
     */
    restoreCustomSizes() {
        if (this.customSizes && this.leftPanel && this.rightPanel) {
            this.leftPanel.style.flexBasis = `${this.customSizes.leftPercentage}%`;
            this.rightPanel.style.flexBasis = `${this.customSizes.rightPercentage}%`;
            this.leftPanel.style.flexGrow = '0';
            this.leftPanel.style.flexShrink = '0';
            this.rightPanel.style.flexGrow = '0';
            this.rightPanel.style.flexShrink = '0';
        }
    }

    /**
     * Enable the resizable divider
     */
    enable() {
        this.enabled = true;
    }

    /**
     * Disable the resizable divider
     */
    disable() {
        this.enabled = false;
        this.resetPanelSizes();

        if (this.isDragging) {
            this.handleMouseUp();
        }
    }

    /**
     * Destroy the resizable divider and clean up event listeners
     */
    destroy() {
        // Remove event listeners
        if (this.divider) {
            this.divider.removeEventListener('mousedown', this.handleMouseDown);
        }

        document.removeEventListener('mousemove', this.handleMouseMove);
        document.removeEventListener('mouseup', this.handleMouseUp);
        window.removeEventListener('resize', this.handleResize);

        // Reset panel sizes
        this.resetPanelSizes();

        // Clear references
        this.divider = null;
        this.leftPanel = null;
        this.rightPanel = null;
        this.container = null;
    }
}