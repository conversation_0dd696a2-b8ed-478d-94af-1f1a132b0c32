/* Chat Application Styles */

/* Main Chat Container */
.chat-app {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 70px);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f5f5f5;
    border-top: 1px solid rgba(0, 0, 0, 0.3);
}
.headerCategoriesMenu .header__categories{
    display: none !important;
}
/* App Header */
.app-header {
    background-color: white;
    border-bottom: 1px solid #e0e0e0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 15px 20px;
    max-width: 100%;
}

.app-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.app-title i {
    color: #4CAF50;
}

/* Panels Container */
.panels-container {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Chat Panels */
.chat-panel {
    display: flex;
    flex-direction: column;
    background-color: white;
    border-radius: 8px;
    margin: 2px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.left-panel {
    flex: 1;
    min-width: 250px;
    transition: all 0.3s ease;
}

.right-panel {
    flex: 1;
    min-width: 400px;
    transition: all 0.3s ease;
}

/* Panel State Classes */
.left-panel-full {
    flex: 1;
    width: 100% !important;
}

.left-panel-half {
    flex: 1;
    width: 50% !important;
}

.right-panel-hidden {
    display: none !important;
}

.right-panel-visible {
    display: flex !important;
    flex: 1 !important;
}

.divider-hidden {
    display: none !important;
}

/* Panel Headers */
.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    background-color: #fafafa;
    border-radius: 8px 8px 0 0;
}

.panel-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.panel-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}
.toggle-wrapper{
    display: flex;
    align-items: center;
    gap: 12px;
}
/* Toggle Button Styles */
.toggle-btn {
    position: relative;
    width: 50px;
    height: 20px;
    background-color: #e0e0e0;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.1s ease;
    outline: none;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-btn::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    width: 14px;
    height: 14px;
    background-color: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-btn:hover {
    background-color: #d0d0d0;
}

.toggle-btn:active {
    transform: scale(0.95);
}

/* Toggle Button Active State */
.toggle-btn.active {
    background-color: #4CAF50;
}

.toggle-btn.active::before {
    transform: translateX(30px);
}

.toggle-btn.active:hover {
    background-color: #45a049;
}

/* Toggle Button with Icons */
.toggle-btn::after {
    content: '\f00d'; /* Font Awesome times icon */
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    right: 8px;
    transform: translateY(-50%);
    font-size: 12px;
    color: #666;
    transition: all 0.3s ease;
}

.toggle-btn.active::after {
    content: '\f00c'; /* Font Awesome check icon */
    left: 8px;
    right: auto;
    color: white;
}

.toggle-label-color{
    color: #4CAF50;
}

/* Divider Styles */
.divider {
    width: 4px;
    background-color: #e0e0e0;
    cursor: col-resize;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 5px 0;
    border-radius: 2px;
    transition: background-color 0.2s ease;
}

.divider:hover {
    background-color: #ccc;
}

.divider.dragging {
    background-color: #9183ec;
    width: 6px;
}

.divider-handle {
    color: #999;
    font-size: 12px;
}

/* Empty State */
.empty-state {
    text-align: center;
    color: #666;
    padding: 40px 20px;
}

.empty-state i {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 15px;
}

.empty-state p {
    margin: 10px 0 5px 0;
    font-size: 16px;
}

.empty-state small {
    font-size: 14px;
    color: #999;
}

/* Chat Container */
.chat-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Chat Messages Styles */
.chat-message {
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
    word-wrap: break-word;
}

.chat-message.user {
    align-self: flex-end;
    align-items: flex-end;
}

.chat-message.bot {
    align-self: flex-start;
    align-items: flex-start;
}

.chat-message-content {
    padding: 12px 16px;
    border-radius: 5px;
    font-size: 14px;
    line-height: 1.4;
    position: relative;
}

.chat-message.user .chat-message-content {
    background-color: #efecfe;
    color: #333;
}

.chat-message.bot .chat-message-content {
    background-color: #d8d8ffe6;
    color: #333;
}
.is-typing {
    width: 50px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding-bottom: 20px;
    scroll-behavior: smooth;
    margin-top: 12px;
}
.jump1,
.jump2,
.jump3 {
    width: 10px;
    height: 10px;
    border-radius: 100%;
    background-color: #2F75FE;
}
.jump1 {
    animation: typing 1.5s linear infinite;
    animation-delay: 0.1s;
}
.jump2 {
    animation: typing 1.5s linear infinite;
    animation-delay: 0.3s;
}
.jump3 {
    animation: typing 1.5s linear infinite;
    animation-delay: 0.5s;
}

@keyframes typing {
    0% {
        transform: translateY(0px);
    }
    25% {
        transform: translateY(0px);
    }
    35% {
        transform: translateY(8px);
    }
    45% {
        transform: translateY(0px);
    }
    60% {
        transform: translateY(-8px);
    }
    75% {
        background-color: white;
        transform: translateY(0px);
    }
    100% {
        transform: translateY(0px);
    }
}
/* Responsive Design */
@media (max-width: 1024px) {
    /* Tablet and Mobile: Default to full screen left panel */
    .panels-container .left-panel {
        width: 100% !important;
        height: 100% !important;
    }

    .panels-container .right-panel {
        display: none !important;
    }

    .panels-container .divider {
        display: none !important;
    }

    /* When toggle is off in mobile/tablet - hide left panel, show right panel */
    .mobile-right-panel-active .panels-container .left-panel {
        display: none !important;
    }

    .mobile-right-panel-active .panels-container .right-panel {
        display: flex !important;
        width: 100% !important;
        height: 100% !important;
        min-width: auto;
    }
}

@media (max-width: 768px) {
    .header-content {
        padding: 12px 15px;
    }

    .app-title {
        font-size: 16px;
    }

    .toggle-wrapper {
        gap: 8px;
    }

    .toggle-label {
        font-size: 12px;
    }
}

@media (max-width: 768px) {
    .chat-app {
        flex-direction: column;
    }
}